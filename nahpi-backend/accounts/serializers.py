from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import User, Department, UserProfile
import re


class DepartmentSerializer(serializers.ModelSerializer):
    """Serializer for Department model"""
    
    class Meta:
        model = Department
        fields = ['id', 'name', 'code', 'description', 'email', 'phone_number', 'is_active']
        read_only_fields = ['id']


class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer for UserProfile model"""
    
    class Meta:
        model = UserProfile
        fields = [
            'avatar', 'bio', 'date_of_birth', 'address',
            'emergency_contact_name', 'emergency_contact_phone', 
            'emergency_contact_relationship', 'email_notifications', 
            'sms_notifications'
        ]


class UserSerializer(serializers.ModelSerializer):
    """Serializer for User model"""
    profile = UserProfileSerializer(read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    full_name = serializers.CharField(read_only=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'username', 'first_name', 'last_name', 'full_name',
            'phone_number', 'role', 'is_verified', 'verification_status',
            'student_id', 'department', 'department_name', 'level',
            'email_verified', 'phone_verified', 'created_at', 'last_login_at',
            'profile'
        ]
        read_only_fields = [
            'id', 'is_verified', 'verification_status', 'email_verified', 
            'phone_verified', 'created_at', 'last_login_at'
        ]


class StudentRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for student registration"""
    password = serializers.CharField(write_only=True, validators=[validate_password])
    confirmPassword = serializers.CharField(write_only=True)
    name = serializers.CharField(write_only=True)
    matricule = serializers.CharField(write_only=True)
    department = serializers.CharField(write_only=True)  # Department name, not code
    yearOfStudy = serializers.CharField(write_only=True)
    phoneNumber = serializers.CharField(write_only=True)
    academicYear = serializers.CharField(write_only=True, required=False)
    verificationMethod = serializers.ChoiceField(choices=['email', 'phone'], default='email', write_only=True)

    class Meta:
        model = User
        fields = [
            'email', 'name', 'matricule', 'department', 'yearOfStudy',
            'phoneNumber', 'academicYear', 'password', 'confirmPassword', 'verificationMethod'
        ]
    
    def validate_email(self, value):
        """Validate email format and uniqueness"""
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("A user with this email already exists.")
        return value
    
    def validate_phoneNumber(self, value):
        """Validate phone number format and uniqueness"""
        # Remove any non-digit characters except +
        cleaned_phone = re.sub(r'[^\d+]', '', value)

        if not re.match(r'^\+?1?\d{9,15}$', cleaned_phone):
            raise serializers.ValidationError(
                "Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
            )

        if User.objects.filter(phone_number=cleaned_phone).exists():
            raise serializers.ValidationError("A user with this phone number already exists.")

        return cleaned_phone

    def validate_matricule(self, value):
        """Validate matricule number uniqueness"""
        if User.objects.filter(student_id=value).exists():
            raise serializers.ValidationError("A user with this matricule number already exists.")
        return value

    def validate_department(self, value):
        """Validate department exists by name"""
        try:
            Department.objects.get(name=value, is_active=True)
        except Department.DoesNotExist:
            raise serializers.ValidationError("Invalid department name.")
        return value
    
    def validate(self, attrs):
        """Validate password confirmation"""
        if attrs['password'] != attrs['confirmPassword']:
            raise serializers.ValidationError("Password confirmation does not match.")
        return attrs
    
    def create(self, validated_data):
        """Create new student user"""
        # Extract and process frontend fields
        confirmPassword = validated_data.pop('confirmPassword')
        name = validated_data.pop('name')
        matricule = validated_data.pop('matricule')
        department_name = validated_data.pop('department')
        yearOfStudy = validated_data.pop('yearOfStudy')
        phoneNumber = validated_data.pop('phoneNumber')
        academicYear = validated_data.pop('academicYear', '')
        verificationMethod = validated_data.pop('verificationMethod', 'email')

        # Split name into first and last name
        name_parts = name.strip().split(' ', 1)
        first_name = name_parts[0]
        last_name = name_parts[1] if len(name_parts) > 1 else ''

        # Get department by name
        department = Department.objects.get(name=department_name)

        # Create user
        user = User.objects.create_user(
            email=validated_data['email'],
            username=validated_data['email'],  # Use email as username
            password=validated_data['password'],
            first_name=first_name,
            last_name=last_name,
            phone_number=phoneNumber,
            student_id=matricule,
            level=yearOfStudy,
            department=department,
            role='student'
        )

        # Create user profile with additional data
        UserProfile.objects.create(
            user=user,
            bio=f"Academic Year: {academicYear}" if academicYear else ""
        )

        return user


class AdminLoginSerializer(serializers.Serializer):
    """Serializer for admin login"""
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        """Validate admin credentials"""
        email = attrs.get('email')
        password = attrs.get('password')
        
        if email and password:
            user = authenticate(email=email, password=password)
            
            if not user:
                raise serializers.ValidationError("Invalid email or password.")
            
            if not user.is_active:
                raise serializers.ValidationError("User account is disabled.")
            
            if user.role != 'admin':
                raise serializers.ValidationError("Access denied. Admin privileges required.")
            
            attrs['user'] = user
            return attrs
        
        raise serializers.ValidationError("Must include email and password.")


class DepartmentOfficerLoginSerializer(serializers.Serializer):
    """Serializer for department officer login"""
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        """Validate department officer credentials"""
        email = attrs.get('email')
        password = attrs.get('password')
        
        if email and password:
            user = authenticate(email=email, password=password)
            
            if not user:
                raise serializers.ValidationError("Invalid email or password.")
            
            if not user.is_active:
                raise serializers.ValidationError("User account is disabled.")
            
            if user.role != 'department_officer':
                raise serializers.ValidationError("Access denied. Department officer privileges required.")
            
            attrs['user'] = user
            return attrs
        
        raise serializers.ValidationError("Must include email and password.")


class StudentLoginSerializer(serializers.Serializer):
    """Serializer for student login using matricule number"""
    matricule = serializers.CharField()
    password = serializers.CharField(write_only=True)

    def validate(self, attrs):
        """Validate student credentials using matricule number"""
        matricule = attrs.get('matricule')
        password = attrs.get('password')

        if matricule and password:
            # Find user by student_id (matricule)
            try:
                user = User.objects.get(student_id=matricule, role='student')
            except User.DoesNotExist:
                raise serializers.ValidationError('Invalid matricule number or password.')

            # Check password
            if not user.check_password(password):
                raise serializers.ValidationError('Invalid matricule number or password.')

            if not user.is_active:
                raise serializers.ValidationError('User account is disabled.')

            if not user.is_verified:
                raise serializers.ValidationError('Please verify your account before logging in.')

            attrs['user'] = user
            return attrs

        raise serializers.ValidationError("Must include matricule number and password.")


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """Custom JWT token serializer with user data"""
    
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        
        # Add custom claims
        token['user_id'] = str(user.id)
        token['email'] = user.email
        token['role'] = user.role
        token['is_verified'] = user.is_verified
        
        return token
    
    def validate(self, attrs):
        """Validate user credentials and return tokens with user data"""
        data = super().validate(attrs)
        
        # Add user data to response
        user_serializer = UserSerializer(self.user)
        data['user'] = user_serializer.data
        
        return data


class PasswordResetRequestSerializer(serializers.Serializer):
    """Serializer for password reset request"""
    email = serializers.EmailField()
    
    def validate_email(self, value):
        """Validate email exists"""
        try:
            User.objects.get(email=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("No user found with this email address.")
        return value


class PasswordResetConfirmSerializer(serializers.Serializer):
    """Serializer for password reset confirmation"""
    token = serializers.CharField()
    password = serializers.CharField(validators=[validate_password])
    password_confirm = serializers.CharField()
    
    def validate(self, attrs):
        """Validate password confirmation"""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Password confirmation does not match.")
        return attrs


class EmailVerificationSerializer(serializers.Serializer):
    """Serializer for email verification"""
    token = serializers.CharField()


class PhoneVerificationSerializer(serializers.Serializer):
    """Serializer for phone verification"""
    code = serializers.CharField(max_length=6)
    
    def validate_code(self, value):
        """Validate verification code format"""
        if not value.isdigit() or len(value) != 6:
            raise serializers.ValidationError("Verification code must be 6 digits.")
        return value


class ChangePasswordSerializer(serializers.Serializer):
    """Serializer for changing password"""
    old_password = serializers.CharField()
    new_password = serializers.CharField(validators=[validate_password])
    new_password_confirm = serializers.CharField()

    def validate(self, attrs):
        """Validate password confirmation"""
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("New password confirmation does not match.")
        return attrs

    def validate_old_password(self, value):
        """Validate old password"""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Old password is incorrect.")
        return value


class AdminUserCreateSerializer(serializers.ModelSerializer):
    """Serializer for admin to create users"""
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    department_code = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = User
        fields = [
            'email', 'username', 'first_name', 'last_name', 'phone_number',
            'role', 'student_id', 'level', 'department_code', 'password', 'password_confirm'
        ]

    def validate_email(self, value):
        """Validate email uniqueness"""
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("A user with this email already exists.")
        return value

    def validate_phone_number(self, value):
        """Validate phone number uniqueness"""
        if User.objects.filter(phone_number=value).exists():
            raise serializers.ValidationError("A user with this phone number already exists.")
        return value

    def validate_student_id(self, value):
        """Validate student ID uniqueness"""
        if value and User.objects.filter(student_id=value).exists():
            raise serializers.ValidationError("A user with this student ID already exists.")
        return value

    def validate_department_code(self, value):
        """Validate department exists"""
        if value:
            try:
                Department.objects.get(code=value.upper(), is_active=True)
            except Department.DoesNotExist:
                raise serializers.ValidationError("Invalid department code.")
        return value.upper() if value else None

    def validate(self, attrs):
        """Validate password confirmation and role-specific fields"""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Password confirmation does not match.")

        role = attrs.get('role')

        # Validate role-specific requirements
        if role == 'student':
            if not attrs.get('student_id'):
                raise serializers.ValidationError("Student ID is required for student role.")
            if not attrs.get('department_code'):
                raise serializers.ValidationError("Department is required for student role.")

        if role == 'department_officer':
            if not attrs.get('department_code'):
                raise serializers.ValidationError("Department is required for department officer role.")

        return attrs

    def create(self, validated_data):
        """Create new user"""
        validated_data.pop('password_confirm')
        department_code = validated_data.pop('department_code', None)

        # Get department if provided
        department = None
        if department_code:
            department = Department.objects.get(code=department_code)

        # Create user
        user = User.objects.create_user(
            email=validated_data['email'],
            username=validated_data['username'],
            password=validated_data['password'],
            first_name=validated_data['first_name'],
            last_name=validated_data['last_name'],
            phone_number=validated_data['phone_number'],
            role=validated_data['role'],
            student_id=validated_data.get('student_id'),
            level=validated_data.get('level'),
            department=department,
            is_verified=True,  # Admin-created users are auto-verified
            email_verified=True,
            phone_verified=True,
            verification_status='verified'
        )

        # Create user profile
        UserProfile.objects.create(user=user)

        return user


class AdminUserUpdateSerializer(serializers.ModelSerializer):
    """Serializer for admin to update users"""
    department_code = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = User
        fields = [
            'email', 'username', 'first_name', 'last_name', 'phone_number',
            'role', 'student_id', 'level', 'department_code', 'is_active',
            'is_verified', 'verification_status'
        ]

    def validate_email(self, value):
        """Validate email uniqueness"""
        if User.objects.filter(email=value).exclude(pk=self.instance.pk).exists():
            raise serializers.ValidationError("A user with this email already exists.")
        return value

    def validate_phone_number(self, value):
        """Validate phone number uniqueness"""
        if User.objects.filter(phone_number=value).exclude(pk=self.instance.pk).exists():
            raise serializers.ValidationError("A user with this phone number already exists.")
        return value

    def validate_student_id(self, value):
        """Validate student ID uniqueness"""
        if value and User.objects.filter(student_id=value).exclude(pk=self.instance.pk).exists():
            raise serializers.ValidationError("A user with this student ID already exists.")
        return value

    def update(self, instance, validated_data):
        """Update user"""
        department_code = validated_data.pop('department_code', None)

        # Update department if provided
        if department_code:
            try:
                department = Department.objects.get(code=department_code, is_active=True)
                validated_data['department'] = department
            except Department.DoesNotExist:
                raise serializers.ValidationError("Invalid department code.")

        # Update user fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        instance.save()
        return instance


class UserListSerializer(serializers.ModelSerializer):
    """Serializer for listing users"""
    department_name = serializers.CharField(source='department.name', read_only=True)
    full_name = serializers.CharField(read_only=True)

    class Meta:
        model = User
        fields = [
            'id', 'email', 'username', 'first_name', 'last_name', 'full_name',
            'phone_number', 'role', 'student_id', 'department_name', 'level',
            'is_active', 'is_verified', 'verification_status', 'created_at', 'last_login_at'
        ]


class DepartmentCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating/updating departments"""

    class Meta:
        model = Department
        fields = ['name', 'code', 'description', 'email', 'phone_number', 'head_of_department', 'is_active']

    def validate_code(self, value):
        """Validate department code uniqueness"""
        code = value.upper()
        if self.instance:
            if Department.objects.filter(code=code).exclude(pk=self.instance.pk).exists():
                raise serializers.ValidationError("A department with this code already exists.")
        else:
            if Department.objects.filter(code=code).exists():
                raise serializers.ValidationError("A department with this code already exists.")
        return code

    def validate_name(self, value):
        """Validate department name uniqueness"""
        if self.instance:
            if Department.objects.filter(name=value).exclude(pk=self.instance.pk).exists():
                raise serializers.ValidationError("A department with this name already exists.")
        else:
            if Department.objects.filter(name=value).exists():
                raise serializers.ValidationError("A department with this name already exists.")
        return value

    def validate_head_of_department(self, value):
        """Validate head of department is a department officer"""
        if value and value.role != 'department_officer':
            raise serializers.ValidationError("Head of department must be a department officer.")
        return value
