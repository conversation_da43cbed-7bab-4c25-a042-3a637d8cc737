import random
import string
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes


def generate_verification_code(length=6):
    """Generate a random verification code"""
    return ''.join(random.choices(string.digits, k=length))


def generate_random_password(length=12):
    """Generate a random password"""
    characters = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(random.choices(characters, k=length))


def send_verification_email(user, verification_url):
    """Send email verification email"""
    subject = 'Verify Your Email - NAHPi Complains'
    
    # Create HTML content
    html_message = f"""
    <html>
    <body>
        <h2>Welcome to NAHPi Complains!</h2>
        <p>Hello {user.first_name},</p>
        <p>Thank you for registering with NAHPi Complains. Please click the button below to verify your email address:</p>
        <p>
            <a href="{verification_url}" 
               style="background-color: #08387F; color: white; padding: 12px 24px; 
                      text-decoration: none; border-radius: 4px; display: inline-block;">
                Verify Email Address
            </a>
        </p>
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p><a href="{verification_url}">{verification_url}</a></p>
        <p>This link will expire in 24 hours for security reasons.</p>
        <p>If you didn't create an account with us, please ignore this email.</p>
        <br>
        <p>Best regards,<br>NAHPi Complains Team</p>
    </body>
    </html>
    """
    
    # Create plain text version
    plain_message = f"""
    Welcome to NAHPi Complains!
    
    Hello {user.first_name},
    
    Thank you for registering with NAHPi Complains. Please click the link below to verify your email address:
    
    {verification_url}
    
    This link will expire in 24 hours for security reasons.
    
    If you didn't create an account with us, please ignore this email.
    
    Best regards,
    NAHPi Complains Team
    """
    
    try:
        send_mail(
            subject=subject,
            message=plain_message,
            html_message=html_message,
            from_email=settings.EMAIL_HOST_USER,
            recipient_list=[user.email],
            fail_silently=False,
        )
        return True
    except Exception as e:
        print(f"Failed to send verification email: {e}")
        return False


def send_password_reset_email(user, reset_url):
    """Send password reset email"""
    subject = 'Password Reset - NAHPi Complains'
    
    # Create HTML content
    html_message = f"""
    <html>
    <body>
        <h2>Password Reset Request</h2>
        <p>Hello {user.first_name},</p>
        <p>You have requested to reset your password for your NAHPi Complains account.</p>
        <p>Please click the button below to reset your password:</p>
        <p>
            <a href="{reset_url}" 
               style="background-color: #08387F; color: white; padding: 12px 24px; 
                      text-decoration: none; border-radius: 4px; display: inline-block;">
                Reset Password
            </a>
        </p>
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p><a href="{reset_url}">{reset_url}</a></p>
        <p>This link will expire in 1 hour for security reasons.</p>
        <p>If you didn't request a password reset, please ignore this email and your password will remain unchanged.</p>
        <br>
        <p>Best regards,<br>NAHPi Complains Team</p>
    </body>
    </html>
    """
    
    # Create plain text version
    plain_message = f"""
    Password Reset Request
    
    Hello {user.first_name},
    
    You have requested to reset your password for your NAHPi Complains account.
    
    Please click the link below to reset your password:
    
    {reset_url}
    
    This link will expire in 1 hour for security reasons.
    
    If you didn't request a password reset, please ignore this email and your password will remain unchanged.
    
    Best regards,
    NAHPi Complains Team
    """
    
    try:
        send_mail(
            subject=subject,
            message=plain_message,
            html_message=html_message,
            from_email=settings.EMAIL_HOST_USER,
            recipient_list=[user.email],
            fail_silently=False,
        )
        return True
    except Exception as e:
        print(f"Failed to send password reset email: {e}")
        return False


def send_sms_verification(phone_number, code):
    """Send SMS verification code (placeholder for SMS service integration)"""
    # In production, integrate with SMS service like Twilio, AWS SNS, etc.
    print(f"SMS to {phone_number}: Your NAHPi Complains verification code is: {code}")
    return True


def generate_password_reset_token(user):
    """Generate password reset token and uidb64"""
    token = default_token_generator.make_token(user)
    uidb64 = urlsafe_base64_encode(force_bytes(user.pk))
    return token, uidb64


def create_admin_user(email, password, first_name, last_name, phone_number=None):
    """Create an admin user"""
    from .models import User
    import uuid

    # Generate unique phone number if not provided
    if phone_number is None:
        phone_number = f"+1555{str(uuid.uuid4().int)[:7]}"

    admin = User.objects.create_user(
        email=email,
        username=email,
        password=password,
        first_name=first_name,
        last_name=last_name,
        phone_number=phone_number,
        role='admin',
        is_verified=True,
        email_verified=True,
        phone_verified=True,
        verification_status='verified'
    )
    return admin


def create_department_officer(email, password, first_name, last_name, department, phone_number):
    """Create a department officer user"""
    from .models import User, UserProfile
    
    officer = User.objects.create_user(
        email=email,
        username=email,
        password=password,
        first_name=first_name,
        last_name=last_name,
        phone_number=phone_number,
        department=department,
        role='department_officer',
        is_verified=True,
        email_verified=True,
        phone_verified=True,
        verification_status='verified'
    )
    
    # Create profile
    UserProfile.objects.create(user=officer)
    
    return officer


def validate_student_id_format(student_id):
    """Validate student ID format (customize based on your institution's format)"""
    # Example: NAH/2023/001234
    import re
    pattern = r'^NAH/\d{4}/\d{6}$'
    return re.match(pattern, student_id) is not None


def get_user_permissions(user):
    """Get user permissions based on role"""
    permissions = {
        'can_submit_complaints': False,
        'can_view_all_complaints': False,
        'can_manage_complaints': False,
        'can_view_reports': False,
        'can_manage_users': False,
        'can_manage_departments': False,
    }
    
    if user.role == 'student':
        permissions.update({
            'can_submit_complaints': user.is_verified,
        })
    elif user.role == 'department_officer':
        permissions.update({
            'can_view_all_complaints': True,
            'can_manage_complaints': True,
            'can_view_reports': True,
        })
    elif user.role == 'admin':
        permissions.update({
            'can_view_all_complaints': True,
            'can_manage_complaints': True,
            'can_view_reports': True,
            'can_manage_users': True,
            'can_manage_departments': True,
        })
    
    return permissions


def bulk_create_users_from_csv(csv_file, created_by):
    """Bulk create users from CSV file"""
    import csv
    import io
    from .models import User, Department, UserProfile

    results = {
        'created': 0,
        'errors': [],
        'users': []
    }

    try:
        # Read CSV file
        csv_data = csv_file.read().decode('utf-8')
        csv_reader = csv.DictReader(io.StringIO(csv_data))

        for row_num, row in enumerate(csv_reader, start=2):  # Start from 2 (header is row 1)
            try:
                # Validate required fields
                required_fields = ['email', 'first_name', 'last_name', 'role']
                for field in required_fields:
                    if not row.get(field):
                        raise ValueError(f"Missing required field: {field}")

                # Check if user already exists
                if User.objects.filter(email=row['email']).exists():
                    results['errors'].append(f"Row {row_num}: User with email {row['email']} already exists")
                    continue

                # Get department if provided
                department = None
                if row.get('department_code'):
                    try:
                        department = Department.objects.get(code=row['department_code'].upper())
                    except Department.DoesNotExist:
                        results['errors'].append(f"Row {row_num}: Invalid department code {row['department_code']}")
                        continue

                # Generate username if not provided
                username = row.get('username', row['email'].split('@')[0])

                # Generate password if not provided
                password = row.get('password', generate_random_password())

                # Create user
                user = User.objects.create_user(
                    email=row['email'],
                    username=username,
                    password=password,
                    first_name=row['first_name'],
                    last_name=row['last_name'],
                    phone_number=row.get('phone_number', ''),
                    role=row['role'],
                    student_id=row.get('student_id'),
                    level=row.get('level'),
                    department=department,
                    is_verified=True,  # Bulk created users are auto-verified
                    email_verified=True,
                    phone_verified=True,
                    verification_status='verified'
                )

                # Create user profile
                UserProfile.objects.create(user=user)

                results['created'] += 1
                results['users'].append({
                    'email': user.email,
                    'password': password,  # Include generated password
                    'role': user.role
                })

            except Exception as e:
                results['errors'].append(f"Row {row_num}: {str(e)}")
                continue

    except Exception as e:
        results['errors'].append(f"CSV processing error: {str(e)}")

    return results


def export_users_to_csv():
    """Export users to CSV format"""
    import csv
    import io
    from .models import User

    output = io.StringIO()
    writer = csv.writer(output)

    # Write header
    writer.writerow([
        'Email', 'Username', 'First Name', 'Last Name', 'Phone Number',
        'Role', 'Student ID', 'Level', 'Department', 'Is Active',
        'Is Verified', 'Created At', 'Last Login'
    ])

    # Write user data
    users = User.objects.select_related('department').all()
    for user in users:
        writer.writerow([
            user.email,
            user.username,
            user.first_name,
            user.last_name,
            user.phone_number,
            user.role,
            user.student_id or '',
            user.level or '',
            user.department.name if user.department else '',
            user.is_active,
            user.is_verified,
            user.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            user.last_login_at.strftime('%Y-%m-%d %H:%M:%S') if user.last_login_at else ''
        ])

    return output.getvalue()


def send_bulk_notification_email(users, subject, message, html_message=None):
    """Send bulk notification emails to users"""
    from django.core.mail import send_mass_mail
    from django.conf import settings

    messages = []
    for user in users:
        # Personalize message
        personalized_message = message.replace('{first_name}', user.first_name)
        personalized_message = personalized_message.replace('{last_name}', user.last_name)
        personalized_message = personalized_message.replace('{email}', user.email)

        messages.append((
            subject,
            personalized_message,
            settings.EMAIL_HOST_USER,
            [user.email]
        ))

    try:
        send_mass_mail(messages, fail_silently=False)
        return True
    except Exception as e:
        print(f"Failed to send bulk emails: {e}")
        return False


def generate_user_report(filters=None):
    """Generate comprehensive user report"""
    from django.db.models import Count, Q
    from datetime import datetime, timedelta
    from .models import User, Department

    # Apply filters
    queryset = User.objects.all()
    if filters:
        if filters.get('role'):
            queryset = queryset.filter(role=filters['role'])
        if filters.get('department'):
            queryset = queryset.filter(department__code=filters['department'])
        if filters.get('is_verified') is not None:
            queryset = queryset.filter(is_verified=filters['is_verified'])
        if filters.get('date_from'):
            queryset = queryset.filter(created_at__gte=filters['date_from'])
        if filters.get('date_to'):
            queryset = queryset.filter(created_at__lte=filters['date_to'])

    # Generate report data
    report = {
        'summary': {
            'total_users': queryset.count(),
            'active_users': queryset.filter(is_active=True).count(),
            'verified_users': queryset.filter(is_verified=True).count(),
            'students': queryset.filter(role='student').count(),
            'department_officers': queryset.filter(role='department_officer').count(),
            'admins': queryset.filter(role='admin').count(),
        },
        'by_department': list(
            queryset.filter(department__isnull=False)
            .values('department__name', 'department__code')
            .annotate(count=Count('id'))
            .order_by('-count')
        ),
        'verification_status': {
            'verified': queryset.filter(is_verified=True).count(),
            'pending': queryset.filter(is_verified=False).count(),
            'email_verified': queryset.filter(email_verified=True).count(),
            'phone_verified': queryset.filter(phone_verified=True).count(),
        },
        'registration_trends': [],
        'generated_at': datetime.now().isoformat(),
        'filters_applied': filters or {}
    }

    # Registration trends (last 12 months)
    for i in range(12):
        month_start = datetime.now().replace(day=1) - timedelta(days=30*i)
        month_end = month_start.replace(day=28) + timedelta(days=4)
        month_end = month_end - timedelta(days=month_end.day)

        count = queryset.filter(
            created_at__gte=month_start,
            created_at__lte=month_end
        ).count()

        report['registration_trends'].append({
            'month': month_start.strftime('%Y-%m'),
            'count': count
        })

    report['registration_trends'].reverse()  # Show oldest first

    return report


def cleanup_unverified_users(days_old=30):
    """Clean up unverified users older than specified days"""
    from datetime import datetime, timedelta
    from .models import User

    cutoff_date = datetime.now() - timedelta(days=days_old)

    # Find unverified users older than cutoff date
    unverified_users = User.objects.filter(
        is_verified=False,
        created_at__lt=cutoff_date
    )

    count = unverified_users.count()
    unverified_users.delete()

    return count
