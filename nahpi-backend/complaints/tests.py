from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
import json

from .models import Complaint, ComplaintAttachment, ComplaintComment, ComplaintFeedback
from accounts.models import Department
from accounts.utils import create_admin_user

User = get_user_model()


class ComplaintModelTest(TestCase):
    """Test Complaint model functionality"""

    def setUp(self):
        self.department = Department.objects.create(
            name='Computer Science',
            code='CSC',
            description='Computer Science Department'
        )

        self.student = User.objects.create_user(
            email='<EMAIL>',
            username='student',
            password='testpass123',
            first_name='<PERSON>',
            last_name='Doe',
            phone_number='+***********',
            role='student',
            department=self.department,
            is_verified=True
        )

    def test_create_complaint(self):
        """Test creating a complaint"""
        complaint = Complaint.objects.create(
            title='Test Complaint',
            description='This is a test complaint',
            complaint_type='academic',
            complainant=self.student,
            department=self.department
        )

        self.assertEqual(complaint.title, 'Test Complaint')
        self.assertEqual(complaint.status, 'submitted')
        self.assertEqual(complaint.priority, 'medium')
        self.assertIsNotNone(complaint.complaint_number)
        self.assertTrue(complaint.complaint_number.startswith('NAH'))

    def test_complaint_number_generation(self):
        """Test unique complaint number generation"""
        complaint1 = Complaint.objects.create(
            title='Complaint 1',
            description='First complaint',
            complaint_type='academic',
            complainant=self.student,
            department=self.department
        )

        complaint2 = Complaint.objects.create(
            title='Complaint 2',
            description='Second complaint',
            complaint_type='administrative',
            complainant=self.student,
            department=self.department
        )

        self.assertNotEqual(complaint1.complaint_number, complaint2.complaint_number)
        self.assertTrue(complaint1.complaint_number.startswith('NAH'))
        self.assertTrue(complaint2.complaint_number.startswith('NAH'))

    def test_complaint_days_since_submission(self):
        """Test days since submission calculation"""
        # Create complaint 5 days ago
        past_date = timezone.now() - timedelta(days=5)
        complaint = Complaint.objects.create(
            title='Old Complaint',
            description='This is an old complaint',
            complaint_type='academic',
            complainant=self.student,
            department=self.department
        )
        complaint.created_at = past_date
        complaint.save()

        self.assertEqual(complaint.days_since_submission, 5)

    def test_complaint_is_overdue(self):
        """Test overdue complaint detection"""
        # Create complaint 8 days ago
        past_date = timezone.now() - timedelta(days=8)
        complaint = Complaint.objects.create(
            title='Overdue Complaint',
            description='This complaint is overdue',
            complaint_type='academic',
            complainant=self.student,
            department=self.department
        )
        complaint.created_at = past_date
        complaint.save()

        self.assertTrue(complaint.is_overdue)


class ComplaintAPITest(APITestCase):
    """Test Complaint API endpoints"""

    def setUp(self):
        self.department = Department.objects.create(
            name='Computer Science',
            code='CSC',
            description='Computer Science Department'
        )

        self.student = User.objects.create_user(
            email='<EMAIL>',
            username='student',
            password='testpass123',
            first_name='John',
            last_name='Doe',
            phone_number='+15551234567',
            role='student',
            department=self.department,
            is_verified=True
        )

        self.admin = create_admin_user(
            email='<EMAIL>',
            password='adminpass123',
            first_name='Admin',
            last_name='User'
        )

        self.department_officer = User.objects.create_user(
            email='<EMAIL>',
            username='officer',
            password='testpass123',
            first_name='Officer',
            last_name='User',
            phone_number='+15551234568',
            role='department_officer',
            department=self.department,
            is_verified=True
        )

    def test_create_complaint_as_student(self):
        """Test creating complaint as verified student"""
        self.client.force_authenticate(user=self.student)

        url = reverse('complaints:complaint_list_create')
        data = {
            'title': 'Test Complaint',
            'description': 'This is a test complaint',
            'complaint_type': 'academic',
            'department_code': 'CSC',
            'incident_location': 'Lecture Hall 1',
            'is_urgent': False
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('complaint', response.data)
        self.assertEqual(response.data['complaint']['title'], 'Test Complaint')
        self.assertTrue(Complaint.objects.filter(title='Test Complaint').exists())

    def test_create_complaint_unverified_student(self):
        """Test creating complaint as unverified student should fail"""
        self.student.is_verified = False
        self.student.save()

        self.client.force_authenticate(user=self.student)

        url = reverse('complaints:complaint_list_create')
        data = {
            'title': 'Test Complaint',
            'description': 'This is a test complaint',
            'complaint_type': 'academic',
            'department_code': 'CSC'
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_list_complaints_as_student(self):
        """Test listing complaints as student (only own complaints)"""
        # Create complaints for different users
        complaint1 = Complaint.objects.create(
            title='Student Complaint',
            description='Student\'s complaint',
            complaint_type='academic',
            complainant=self.student,
            department=self.department
        )

        other_student = User.objects.create_user(
            email='<EMAIL>',
            username='other',
            password='testpass123',
            phone_number='+15551234569',
            role='student',
            is_verified=True
        )

        complaint2 = Complaint.objects.create(
            title='Other Student Complaint',
            description='Other student\'s complaint',
            complaint_type='academic',
            complainant=other_student,
            department=self.department
        )

        self.client.force_authenticate(user=self.student)

        url = reverse('complaints:complaint_list_create')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['title'], 'Student Complaint')

    def test_update_complaint_status_as_admin(self):
        """Test updating complaint status as admin"""
        complaint = Complaint.objects.create(
            title='Test Complaint',
            description='Test complaint',
            complaint_type='academic',
            complainant=self.student,
            department=self.department
        )

        self.client.force_authenticate(user=self.admin)

        url = reverse('complaints:update_complaint_status', kwargs={'complaint_id': complaint.id})
        data = {
            'status': 'under_review',
            'comment': 'Starting review process'
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        complaint.refresh_from_db()
        self.assertEqual(complaint.status, 'under_review')

    def test_assign_complaint_as_department_officer(self):
        """Test assigning complaint as department officer"""
        complaint = Complaint.objects.create(
            title='Test Complaint',
            description='Test complaint',
            complaint_type='academic',
            complainant=self.student,
            department=self.department
        )

        self.client.force_authenticate(user=self.department_officer)

        url = reverse('complaints:assign_complaint', kwargs={'complaint_id': complaint.id})
        data = {
            'assignee_id': str(self.department_officer.id)
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        complaint.refresh_from_db()
        self.assertEqual(complaint.assigned_to, self.department_officer)


class ComplaintAttachmentTest(APITestCase):
    """Test complaint attachment functionality"""

    def setUp(self):
        self.department = Department.objects.create(
            name='Computer Science',
            code='CSC',
            description='Computer Science Department'
        )

        self.student = User.objects.create_user(
            email='<EMAIL>',
            username='student',
            password='testpass123',
            phone_number='+15551234570',
            role='student',
            department=self.department,
            is_verified=True
        )

        self.complaint = Complaint.objects.create(
            title='Test Complaint',
            description='Test complaint',
            complaint_type='academic',
            complainant=self.student,
            department=self.department
        )

    def test_upload_file_attachment(self):
        """Test uploading file attachment"""
        self.client.force_authenticate(user=self.student)

        # Create a test file
        test_file = SimpleUploadedFile(
            "test.txt",
            b"This is a test file content",
            content_type="text/plain"
        )

        url = reverse('complaints:complaint_attachments', kwargs={'complaint_id': self.complaint.id})
        data = {'files': [test_file]}

        response = self.client.post(url, data, format='multipart')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['uploaded_count'], 1)
        self.assertTrue(ComplaintAttachment.objects.filter(complaint=self.complaint).exists())

    def test_upload_invalid_file_type(self):
        """Test uploading invalid file type"""
        self.client.force_authenticate(user=self.student)

        # Create a test file with invalid extension
        test_file = SimpleUploadedFile(
            "test.exe",
            b"This is an executable file",
            content_type="application/octet-stream"
        )

        url = reverse('complaints:complaint_attachments', kwargs={'complaint_id': self.complaint.id})
        data = {'files': [test_file]}

        response = self.client.post(url, data, format='multipart')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('errors', response.data)
        self.assertEqual(len(response.data['errors']), 1)

    def test_download_attachment_permission(self):
        """Test downloading attachment with proper permissions"""
        # Create attachment
        attachment = ComplaintAttachment.objects.create(
            complaint=self.complaint,
            file=SimpleUploadedFile("test.txt", b"content"),
            original_filename="test.txt"
        )

        self.client.force_authenticate(user=self.student)

        url = reverse('complaints:download_attachment', kwargs={
            'complaint_id': self.complaint.id,
            'attachment_id': attachment.id
        })

        response = self.client.get(url)

        # Note: This might fail in test environment due to file storage
        # In production, this would return the file content
        self.assertIn(response.status_code, [status.HTTP_200_OK, status.HTTP_500_INTERNAL_SERVER_ERROR])


class ComplaintCommentTest(APITestCase):
    """Test complaint comment functionality"""

    def setUp(self):
        self.department = Department.objects.create(
            name='Computer Science',
            code='CSC',
            description='Computer Science Department'
        )

        self.student = User.objects.create_user(
            email='<EMAIL>',
            username='student',
            password='testpass123',
            phone_number='+15551234571',
            role='student',
            department=self.department,
            is_verified=True
        )

        self.admin = create_admin_user(
            email='<EMAIL>',
            password='adminpass123',
            first_name='Admin',
            last_name='User'
        )

        self.complaint = Complaint.objects.create(
            title='Test Complaint',
            description='Test complaint',
            complaint_type='academic',
            complainant=self.student,
            department=self.department
        )

    def test_add_comment_as_student(self):
        """Test adding comment as student"""
        self.client.force_authenticate(user=self.student)

        url = reverse('complaints:complaint_comments', kwargs={'complaint_id': self.complaint.id})
        data = {
            'content': 'This is a test comment',
            'is_internal': False
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(ComplaintComment.objects.filter(
            complaint=self.complaint,
            content='This is a test comment'
        ).exists())

    def test_add_internal_comment_as_admin(self):
        """Test adding internal comment as admin"""
        self.client.force_authenticate(user=self.admin)

        url = reverse('complaints:complaint_comments', kwargs={'complaint_id': self.complaint.id})
        data = {
            'content': 'This is an internal comment',
            'is_internal': True
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        comment = ComplaintComment.objects.get(
            complaint=self.complaint,
            content='This is an internal comment'
        )
        self.assertTrue(comment.is_internal)

    def test_student_cannot_see_internal_comments(self):
        """Test that students cannot see internal comments"""
        # Create internal comment
        ComplaintComment.objects.create(
            complaint=self.complaint,
            author=self.admin,
            content='Internal comment',
            is_internal=True
        )

        # Create public comment
        ComplaintComment.objects.create(
            complaint=self.complaint,
            author=self.admin,
            content='Public comment',
            is_internal=False
        )

        self.client.force_authenticate(user=self.student)

        url = reverse('complaints:complaint_comments', kwargs={'complaint_id': self.complaint.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['content'], 'Public comment')


class ComplaintFeedbackTest(APITestCase):
    """Test complaint feedback functionality"""

    def setUp(self):
        self.department = Department.objects.create(
            name='Computer Science',
            code='CSC',
            description='Computer Science Department'
        )

        self.student = User.objects.create_user(
            email='<EMAIL>',
            username='student',
            password='testpass123',
            phone_number='+15551234572',
            role='student',
            department=self.department,
            is_verified=True
        )

        self.complaint = Complaint.objects.create(
            title='Test Complaint',
            description='Test complaint',
            complaint_type='academic',
            complainant=self.student,
            department=self.department,
            status='resolved'
        )

    def test_submit_feedback_for_resolved_complaint(self):
        """Test submitting feedback for resolved complaint"""
        self.client.force_authenticate(user=self.student)

        url = reverse('complaints:complaint_feedback', kwargs={'complaint_id': self.complaint.id})
        data = {
            'satisfaction_rating': 4,
            'feedback_text': 'Good resolution',
            'response_time_rating': 5,
            'resolution_quality_rating': 4,
            'staff_helpfulness_rating': 5,
            'would_recommend': True
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(ComplaintFeedback.objects.filter(complaint=self.complaint).exists())

    def test_submit_feedback_for_pending_complaint(self):
        """Test submitting feedback for pending complaint should fail"""
        self.complaint.status = 'submitted'
        self.complaint.save()

        self.client.force_authenticate(user=self.student)

        url = reverse('complaints:complaint_feedback', kwargs={'complaint_id': self.complaint.id})
        data = {
            'satisfaction_rating': 4,
            'feedback_text': 'Good resolution'
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
