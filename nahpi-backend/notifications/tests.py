from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from .models import Notification, EmailTemplate, NotificationPreference, NotificationLog
from .utils import create_notification, send_notification
from accounts.models import Department
from accounts.utils import create_admin_user
from complaints.models import Complaint

User = get_user_model()


class NotificationModelTest(TestCase):
    """Test Notification model functionality"""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='user',
            password='testpass123',
            first_name='Test',
            last_name='User',
            phone_number='+***********',
            role='student',
            is_verified=True
        )

    def test_create_notification(self):
        """Test creating a notification"""
        notification = Notification.objects.create(
            recipient=self.user,
            title='Test Notification',
            message='This is a test notification',
            notification_type='system_announcement',
            priority='medium'
        )

        self.assertEqual(notification.title, 'Test Notification')
        self.assertEqual(notification.recipient, self.user)
        self.assertFalse(notification.is_read)
        self.assertFalse(notification.is_sent)

    def test_mark_notification_as_read(self):
        """Test marking notification as read"""
        notification = Notification.objects.create(
            recipient=self.user,
            title='Test Notification',
            message='This is a test notification',
            notification_type='system_announcement'
        )

        self.assertFalse(notification.is_read)
        self.assertIsNone(notification.read_at)

        notification.mark_as_read()

        self.assertTrue(notification.is_read)
        self.assertIsNotNone(notification.read_at)

    def test_mark_notification_as_sent(self):
        """Test marking notification as sent"""
        notification = Notification.objects.create(
            recipient=self.user,
            title='Test Notification',
            message='This is a test notification',
            notification_type='system_announcement'
        )

        self.assertFalse(notification.is_sent)
        self.assertIsNone(notification.sent_at)

        notification.mark_as_sent()

        self.assertTrue(notification.is_sent)
        self.assertIsNotNone(notification.sent_at)


class NotificationPreferenceTest(TestCase):
    """Test NotificationPreference model"""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='user',
            password='testpass123',
            phone_number='+15551234581',
            role='student',
            is_verified=True
        )

    def test_create_notification_preference(self):
        """Test creating notification preferences"""
        preference = NotificationPreference.objects.create(
            user=self.user,
            email_complaint_updates=True,
            email_status_changes=False,
            sms_urgent_notifications=True
        )

        self.assertEqual(preference.user, self.user)
        self.assertTrue(preference.email_complaint_updates)
        self.assertFalse(preference.email_status_changes)
        self.assertTrue(preference.sms_urgent_notifications)


class NotificationAPITest(APITestCase):
    """Test Notification API endpoints"""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='user',
            password='testpass123',
            first_name='Test',
            last_name='User',
            phone_number='+15551234582',
            role='student',
            is_verified=True
        )

        self.admin = create_admin_user(
            email='<EMAIL>',
            password='adminpass123',
            first_name='Admin',
            last_name='User'
        )

        # Create test notifications
        self.notification1 = Notification.objects.create(
            recipient=self.user,
            title='Notification 1',
            message='First notification',
            notification_type='system_announcement'
        )

        self.notification2 = Notification.objects.create(
            recipient=self.user,
            title='Notification 2',
            message='Second notification',
            notification_type='complaint_updated',
            is_read=True
        )

    def test_list_user_notifications(self):
        """Test listing user notifications"""
        self.client.force_authenticate(user=self.user)

        url = reverse('notifications:notification_list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)

    def test_filter_unread_notifications(self):
        """Test filtering unread notifications"""
        self.client.force_authenticate(user=self.user)

        url = reverse('notifications:notification_list')
        response = self.client.get(url, {'is_read': 'false'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['title'], 'Notification 1')

    def test_mark_notification_as_read(self):
        """Test marking notification as read via API"""
        self.client.force_authenticate(user=self.user)

        url = reverse('notifications:mark_notification_read', kwargs={
            'notification_id': self.notification1.id
        })
        response = self.client.post(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.notification1.refresh_from_db()
        self.assertTrue(self.notification1.is_read)

    def test_mark_all_notifications_as_read(self):
        """Test marking all notifications as read"""
        self.client.force_authenticate(user=self.user)

        url = reverse('notifications:mark_all_notifications_read')
        response = self.client.post(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that unread notification is now read
        self.notification1.refresh_from_db()
        self.assertTrue(self.notification1.is_read)

    def test_notification_summary(self):
        """Test notification summary endpoint"""
        self.client.force_authenticate(user=self.user)

        url = reverse('notifications:notification_summary')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['total_notifications'], 2)
        self.assertEqual(response.data['unread_notifications'], 1)

    def test_get_notification_preferences(self):
        """Test getting notification preferences"""
        self.client.force_authenticate(user=self.user)

        url = reverse('notifications:notification_preferences')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Should create default preferences if none exist
        self.assertIn('email_complaint_updates', response.data)

    def test_update_notification_preferences(self):
        """Test updating notification preferences"""
        self.client.force_authenticate(user=self.user)

        url = reverse('notifications:notification_preferences')
        data = {
            'email_complaint_updates': False,
            'email_status_changes': True,
            'sms_urgent_notifications': True
        }

        response = self.client.put(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that preferences were updated
        preference = NotificationPreference.objects.get(user=self.user)
        self.assertFalse(preference.email_complaint_updates)
        self.assertTrue(preference.email_status_changes)
        self.assertTrue(preference.sms_urgent_notifications)


class AdminNotificationTest(APITestCase):
    """Test admin notification management"""

    def setUp(self):
        self.admin = create_admin_user(
            email='<EMAIL>',
            password='adminpass123',
            first_name='Admin',
            last_name='User'
        )

        self.user1 = User.objects.create_user(
            email='<EMAIL>',
            username='user1',
            password='testpass123',
            phone_number='+15551234583',
            role='student',
            is_verified=True
        )

        self.user2 = User.objects.create_user(
            email='<EMAIL>',
            username='user2',
            password='testpass123',
            phone_number='+15551234584',
            role='student',
            is_verified=True
        )

    def test_admin_create_notification(self):
        """Test admin creating notification"""
        self.client.force_authenticate(user=self.admin)

        url = reverse('notifications:admin_notification_list')
        data = {
            'title': 'Admin Notification',
            'message': 'This is an admin notification',
            'notification_type': 'system_announcement',
            'priority': 'high',
            'recipient_ids': [str(self.user1.id)],
            'send_email': True
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(Notification.objects.filter(
            title='Admin Notification',
            recipient=self.user1
        ).exists())

    def test_send_bulk_notification(self):
        """Test sending bulk notifications"""
        self.client.force_authenticate(user=self.admin)

        url = reverse('notifications:send_bulk_notification')
        data = {
            'title': 'Bulk Notification',
            'message': 'This is a bulk notification',
            'notification_type': 'system_announcement',
            'send_to_role': 'student',
            'send_email': True
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that notifications were created for all students
        student_notifications = Notification.objects.filter(
            title='Bulk Notification',
            recipient__role='student'
        )
        self.assertEqual(student_notifications.count(), 2)

    def test_notification_analytics(self):
        """Test notification analytics endpoint"""
        # Create some test notifications
        Notification.objects.create(
            recipient=self.user1,
            title='Test 1',
            message='Message 1',
            notification_type='system_announcement',
            is_sent=True
        )

        Notification.objects.create(
            recipient=self.user2,
            title='Test 2',
            message='Message 2',
            notification_type='complaint_updated',
            is_sent=False
        )

        self.client.force_authenticate(user=self.admin)

        url = reverse('notifications:notification_analytics')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['total_notifications'], 2)
        self.assertEqual(response.data['sent_notifications'], 1)
        self.assertEqual(response.data['pending_notifications'], 1)
