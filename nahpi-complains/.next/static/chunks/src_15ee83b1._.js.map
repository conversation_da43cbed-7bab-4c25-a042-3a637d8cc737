{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/complaint_sys/nahpi-complains/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date | string): string {\n  const d = new Date(date)\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nexport function formatDateTime(date: Date | string): string {\n  const d = new Date(date)\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  })\n}\n\nexport function getStatusColor(status: string): string {\n  switch (status.toLowerCase()) {\n    case 'pending':\n    case 'unresolved':\n      return 'bg-yellow-100 text-yellow-800 border-yellow-200'\n    case 'in progress':\n    case 'processing':\n      return 'bg-blue-100 text-blue-800 border-blue-200'\n    case 'resolved':\n      return 'bg-green-100 text-green-800 border-green-200'\n    case 'rejected':\n      return 'bg-red-100 text-red-800 border-red-200'\n    default:\n      return 'bg-gray-100 text-gray-800 border-gray-200'\n  }\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase())\n    .join('')\n    .slice(0, 2)\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function validatePhone(phone: string): boolean {\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/\n  return phoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\nexport function generateComplaintId(): string {\n  const timestamp = Date.now().toString(36)\n  const random = Math.random().toString(36).substr(2, 5)\n  return `CMP-${timestamp}-${random}`.toUpperCase()\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength) + '...'\n}\n\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAQ,OAAO,WAAW;QACxB,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC,IACL,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAEO,SAAS;IACd,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,CAAC;IACtC,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;IACpD,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,QAAQ,CAAC,WAAW;AACjD;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;AAC/D;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/complaint_sys/nahpi-complains/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'\n  size?: 'sm' | 'md' | 'lg'\n  isLoading?: boolean\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ \n    className, \n    variant = 'primary', \n    size = 'md', \n    isLoading = false,\n    leftIcon,\n    rightIcon,\n    children, \n    disabled,\n    ...props \n  }, ref) => {\n    const baseStyles = \"inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none\"\n    \n    const variants = {\n      primary: \"bg-primary text-white hover:bg-primary-dark focus:ring-primary\",\n      secondary: \"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500\",\n      outline: \"border border-primary text-primary hover:bg-primary hover:text-white focus:ring-primary\",\n      ghost: \"text-primary hover:bg-accent-blue focus:ring-primary\",\n      danger: \"bg-error text-white hover:bg-red-600 focus:ring-error\"\n    }\n    \n    const sizes = {\n      sm: \"h-8 px-3 text-sm\",\n      md: \"h-10 px-4 text-sm\",\n      lg: \"h-12 px-6 text-base\"\n    }\n\n    return (\n      <button\n        className={cn(\n          baseStyles,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {!isLoading && leftIcon && (\n          <span className=\"mr-2\">{leftIcon}</span>\n        )}\n        {children}\n        {!isLoading && rightIcon && (\n          <span className=\"ml-2\">{rightIcon}</span>\n        )}\n      </button>\n    )\n  }\n)\n\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAUA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EACC,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,KAAK,EACjB,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,aAAa;IAEnB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,2BACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP,CAAC,aAAa,0BACb,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;YAEzB;YACA,CAAC,aAAa,2BACb,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAIhC;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/complaint_sys/nahpi-complains/src/components/ui/Badge.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'default' | 'success' | 'warning' | 'error' | 'info' | 'secondary'\n  size?: 'sm' | 'md' | 'lg'\n}\n\nconst Badge = React.forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant = 'default', size = 'md', ...props }, ref) => {\n    const baseStyles = \"inline-flex items-center rounded-full border font-medium\"\n    \n    const variants = {\n      default: \"bg-gray-100 text-gray-800 border-gray-200\",\n      success: \"bg-green-100 text-green-800 border-green-200\",\n      warning: \"bg-yellow-100 text-yellow-800 border-yellow-200\",\n      error: \"bg-red-100 text-red-800 border-red-200\",\n      info: \"bg-blue-100 text-blue-800 border-blue-200\",\n      secondary: \"bg-accent-blue text-primary border-primary/20\"\n    }\n    \n    const sizes = {\n      sm: \"px-2 py-0.5 text-xs\",\n      md: \"px-2.5 py-1 text-sm\",\n      lg: \"px-3 py-1.5 text-base\"\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          baseStyles,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        {...props}\n      />\n    )\n  }\n)\n\nBadge.displayName = \"Badge\"\n\nexport { Badge }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,GAAG,OAAO,EAAE;IAC1D,MAAM,aAAa;IAEnB,MAAM,WAAW;QACf,SAAS;QACT,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QACN,WAAW;IACb;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAED,GAAG,KAAK;;;;;;AAGf;;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/complaint_sys/nahpi-complains/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/Button'\nimport { Badge } from '@/components/ui/Badge'\nimport { cn } from '@/lib/utils'\n\ninterface HeaderProps {\n  user?: {\n    name: string\n    role: string\n    avatar?: string\n  }\n  notifications?: number\n}\n\nexport function Header({ user, notifications = 0 }: HeaderProps) {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo and Brand */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-primary rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">NC</span>\n              </div>\n              <div className=\"hidden sm:block\">\n                <h1 className=\"text-xl font-bold text-primary\">NAHPi Complains</h1>\n                <p className=\"text-xs text-gray-500\">Complaint Management System</p>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          {user ? (\n            <div className=\"hidden md:flex items-center space-x-4\">\n              {/* Navigation Links */}\n              <nav className=\"flex space-x-4\">\n                <Link \n                  href=\"/dashboard\" \n                  className=\"text-gray-600 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                >\n                  Dashboard\n                </Link>\n                {user.role === 'student' && (\n                  <>\n                    <Link \n                      href=\"/complaints/new\" \n                      className=\"text-gray-600 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                    >\n                      New Complaint\n                    </Link>\n                    <Link \n                      href=\"/complaints\" \n                      className=\"text-gray-600 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                    >\n                      My Complaints\n                    </Link>\n                  </>\n                )}\n                {user.role === 'admin' && (\n                  <>\n                    <Link \n                      href=\"/admin/complaints\" \n                      className=\"text-gray-600 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                    >\n                      All Complaints\n                    </Link>\n                    <Link \n                      href=\"/admin/users\" \n                      className=\"text-gray-600 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                    >\n                      Users\n                    </Link>\n                    <Link \n                      href=\"/admin/reports\" \n                      className=\"text-gray-600 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                    >\n                      Reports\n                    </Link>\n                  </>\n                )}\n                {user.role === 'department_officer' && (\n                  <>\n                    <Link \n                      href=\"/department/complaints\" \n                      className=\"text-gray-600 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                    >\n                      Department Complaints\n                    </Link>\n                    <Link \n                      href=\"/department/assigned\" \n                      className=\"text-gray-600 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                    >\n                      Assigned to Me\n                    </Link>\n                  </>\n                )}\n              </nav>\n\n              {/* Notifications */}\n              <div className=\"relative\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 17h5l-5 5v-5zM12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\" />\n                  </svg>\n                  {notifications > 0 && (\n                    <Badge \n                      variant=\"error\" \n                      size=\"sm\" \n                      className=\"absolute -top-1 -right-1 min-w-[1.25rem] h-5 flex items-center justify-center p-0 text-xs\"\n                    >\n                      {notifications > 99 ? '99+' : notifications}\n                    </Badge>\n                  )}\n                </Button>\n              </div>\n\n              {/* User Menu */}\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"text-right\">\n                  <p className=\"text-sm font-medium text-gray-900\">{user.name}</p>\n                  <p className=\"text-xs text-gray-500 capitalize\">{user.role.replace('_', ' ')}</p>\n                </div>\n                <div className=\"w-8 h-8 bg-primary rounded-full flex items-center justify-center\">\n                  <span className=\"text-white text-sm font-medium\">\n                    {user.name.split(' ').map(n => n[0]).join('').slice(0, 2)}\n                  </span>\n                </div>\n              </div>\n\n              <Button variant=\"ghost\" size=\"sm\">\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                </svg>\n              </Button>\n            </div>\n          ) : (\n            <div className=\"hidden md:flex items-center space-x-4\">\n              <Link href=\"/login\">\n                <Button variant=\"ghost\">Login</Button>\n              </Link>\n              <Link href=\"/register\">\n                <Button variant=\"primary\">Register</Button>\n              </Link>\n            </div>\n          )}\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden border-t border-gray-200 py-4\">\n            {user ? (\n              <div className=\"space-y-2\">\n                <Link \n                  href=\"/dashboard\" \n                  className=\"block px-3 py-2 text-gray-600 hover:text-primary hover:bg-gray-50 rounded-md\"\n                >\n                  Dashboard\n                </Link>\n                {user.role === 'student' && (\n                  <>\n                    <Link \n                      href=\"/complaints/new\" \n                      className=\"block px-3 py-2 text-gray-600 hover:text-primary hover:bg-gray-50 rounded-md\"\n                    >\n                      New Complaint\n                    </Link>\n                    <Link \n                      href=\"/complaints\" \n                      className=\"block px-3 py-2 text-gray-600 hover:text-primary hover:bg-gray-50 rounded-md\"\n                    >\n                      My Complaints\n                    </Link>\n                  </>\n                )}\n                <div className=\"border-t border-gray-200 pt-2 mt-2\">\n                  <div className=\"px-3 py-2\">\n                    <p className=\"text-sm font-medium text-gray-900\">{user.name}</p>\n                    <p className=\"text-xs text-gray-500 capitalize\">{user.role.replace('_', ' ')}</p>\n                  </div>\n                  <button className=\"block w-full text-left px-3 py-2 text-gray-600 hover:text-primary hover:bg-gray-50 rounded-md\">\n                    Logout\n                  </button>\n                </div>\n              </div>\n            ) : (\n              <div className=\"space-y-2\">\n                <Link \n                  href=\"/login\" \n                  className=\"block px-3 py-2 text-gray-600 hover:text-primary hover:bg-gray-50 rounded-md\"\n                >\n                  Login\n                </Link>\n                <Link \n                  href=\"/register\" \n                  className=\"block px-3 py-2 text-gray-600 hover:text-primary hover:bg-gray-50 rounded-md\"\n                >\n                  Register\n                </Link>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAiBO,SAAS,OAAO,EAAE,IAAI,EAAE,gBAAgB,CAAC,EAAe;;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;wBAM1C,qBACC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;wCAGA,KAAK,IAAI,KAAK,2BACb;;8DACE,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;8DAGD,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;wCAKJ,KAAK,IAAI,KAAK,yBACb;;8DACE,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;8DAGD,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;8DAGD,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;wCAKJ,KAAK,IAAI,KAAK,sCACb;;8DACE,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;8DAGD,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;8CAQP,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;;0DAC1C,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CAEtE,gBAAgB,mBACf,6LAAC,oIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,MAAK;gDACL,WAAU;0DAET,gBAAgB,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAqC,KAAK,IAAI;;;;;;8DAC3D,6LAAC;oDAAE,WAAU;8DAAoC,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;sDAE1E,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,KAAK,CAAC,GAAG;;;;;;;;;;;;;;;;;8CAK7D,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;iDAK3E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;kDAAQ;;;;;;;;;;;8CAE1B,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;kDAAU;;;;;;;;;;;;;;;;;sCAMhC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,cAAc,CAAC;0CAE9B,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAO5E,4BACC,6LAAC;oBAAI,WAAU;8BACZ,qBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAGA,KAAK,IAAI,KAAK,2BACb;;kDACE,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;0CAKL,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAqC,KAAK,IAAI;;;;;;0DAC3D,6LAAC;gDAAE,WAAU;0DAAoC,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;kDAE1E,6LAAC;wCAAO,WAAU;kDAAgG;;;;;;;;;;;;;;;;;6CAMtH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GA/MgB;KAAA", "debugId": null}}, {"offset": {"line": 780, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/complaint_sys/nahpi-complains/src/components/layout/Footer.tsx"], "sourcesContent": ["import React from 'react'\nimport Link from 'next/link'\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-primary text-white py-8 mt-auto\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Brand */}\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"w-8 h-8 bg-white rounded-lg flex items-center justify-center\">\n                <span className=\"text-primary font-bold text-sm\">NC</span>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-bold\">NAHPi Complaints</h3>\n                <p className=\"text-blue-100 text-sm\">Complaint Management System</p>\n              </div>\n            </div>\n            <p className=\"text-blue-100 text-sm leading-relaxed\">\n              Streamlining complaint resolution for educational excellence. \n              A comprehensive system designed to facilitate efficient communication \n              between students, administrators, and department officers.\n            </p>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h4 className=\"font-semibold mb-4\">Quick Links</h4>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <Link href=\"/\" className=\"text-blue-100 hover:text-white transition-colors\">\n                  Home\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/login\" className=\"text-blue-100 hover:text-white transition-colors\">\n                  Student Login\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/register\" className=\"text-blue-100 hover:text-white transition-colors\">\n                  Student Registration\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/admin/login\" className=\"text-blue-100 hover:text-white transition-colors\">\n                  Admin Portal\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/department/login\" className=\"text-blue-100 hover:text-white transition-colors\">\n                  Department Portal\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div>\n            <h4 className=\"font-semibold mb-4\">Support</h4>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <a href=\"#\" className=\"text-blue-100 hover:text-white transition-colors\">\n                  Help Center\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-blue-100 hover:text-white transition-colors\">\n                  Contact Support\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-blue-100 hover:text-white transition-colors\">\n                  FAQ\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-blue-100 hover:text-white transition-colors\">\n                  System Status\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-blue-100 hover:text-white transition-colors\">\n                  Privacy Policy\n                </a>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-blue-600 mt-8 pt-6 flex flex-col sm:flex-row justify-between items-center\">\n          <p className=\"text-blue-200 text-sm\">\n            © 2024 NAHPi. All rights reserved.\n          </p>\n          <div className=\"flex space-x-6 mt-4 sm:mt-0\">\n            <a href=\"#\" className=\"text-blue-200 hover:text-white transition-colors\">\n              <span className=\"sr-only\">Terms of Service</span>\n              <span className=\"text-sm\">Terms</span>\n            </a>\n            <a href=\"#\" className=\"text-blue-200 hover:text-white transition-colors\">\n              <span className=\"sr-only\">Privacy Policy</span>\n              <span className=\"text-sm\">Privacy</span>\n            </a>\n            <a href=\"#\" className=\"text-blue-200 hover:text-white transition-colors\">\n              <span className=\"sr-only\">Contact</span>\n              <span className=\"text-sm\">Contact</span>\n            </a>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAEO,SAAS;IACd,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAiC;;;;;;;;;;;sDAEnD,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,6LAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;sCAQvD,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAmD;;;;;;;;;;;sDAI9E,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmD;;;;;;;;;;;sDAInF,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAmD;;;;;;;;;;;sDAItF,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAe,WAAU;0DAAmD;;;;;;;;;;;sDAIzF,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;sCAQlG,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAmD;;;;;;;;;;;sDAI3E,6LAAC;sDACC,cAAA,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAmD;;;;;;;;;;;sDAI3E,6LAAC;sDACC,cAAA,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAmD;;;;;;;;;;;sDAI3E,6LAAC;sDACC,cAAA,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAmD;;;;;;;;;;;sDAI3E,6LAAC;sDACC,cAAA,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASjF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;sCAGrC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,MAAK;oCAAI,WAAU;;sDACpB,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;8CAE5B,6LAAC;oCAAE,MAAK;oCAAI,WAAU;;sDACpB,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;8CAE5B,6LAAC;oCAAE,MAAK;oCAAI,WAAU;;sDACpB,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;KA/GgB", "debugId": null}}, {"offset": {"line": 1195, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/complaint_sys/nahpi-complains/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'default' | 'outlined' | 'elevated'\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant = 'default', ...props }, ref) => {\n    const variants = {\n      default: \"bg-white border border-gray-200\",\n      outlined: \"bg-white border-2 border-gray-300\",\n      elevated: \"bg-white shadow-lg border border-gray-100\"\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"rounded-lg\",\n          variants[variant],\n          className\n        )}\n        {...props}\n      />\n    )\n  }\n)\n\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n      {...props}\n    />\n  )\n)\n\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn(\"text-lg font-semibold leading-none tracking-tight text-gray-900\", className)}\n      {...props}\n    />\n  )\n)\n\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p\n      ref={ref}\n      className={cn(\"text-sm text-gray-600\", className)}\n      {...props}\n    />\n  )\n)\n\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div \n      ref={ref} \n      className={cn(\"p-6 pt-0\", className)} \n      {...props} \n    />\n  )\n)\n\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\"flex items-center p-6 pt-0\", className)}\n      {...props}\n    />\n  )\n)\n\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAMA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAO,EAAE;IAC7C,MAAM,WAAW;QACf,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cACA,QAAQ,CAAC,QAAQ,EACjB;QAED,GAAG,KAAK;;;;;;AAGf;;AAGF,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAKf,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAChC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;;;;;;;AAKf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OACtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAKf,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;;AAKf,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QACjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAKf,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1305, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/complaint_sys/nahpi-complains/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { <PERSON><PERSON> } from '@/components/layout/Header'\nimport { Foot<PERSON> } from '@/components/layout/Footer'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { Badge } from '@/components/ui/Badge'\n\n// Mock data for demonstration\nconst mockUser = {\n  name: '<PERSON>',\n  role: 'student' as const,\n  avatar: undefined\n}\n\nconst mockStats = {\n  totalComplaints: 5,\n  pendingComplaints: 2,\n  resolvedComplaints: 2,\n  rejectedComplaints: 1\n}\n\nconst mockRecentComplaints = [\n  {\n    id: '1',\n    complaintId: 'CMP-2024-001',\n    title: 'CA Mark Discrepancy in Mathematics',\n    status: 'pending' as const,\n    submittedAt: new Date('2024-01-15'),\n    courseCode: 'MATH101',\n    category: 'ca_mark' as const\n  },\n  {\n    id: '2',\n    complaintId: 'CMP-2024-002',\n    title: '<PERSON><PERSON> for Physics',\n    status: 'in_progress' as const,\n    submittedAt: new Date('2024-01-10'),\n    courseCode: 'PHYS201',\n    category: 'exam_mark' as const\n  },\n  {\n    id: '3',\n    complaintId: 'CMP-2024-003',\n    title: 'Course Registration Issue',\n    status: 'resolved' as const,\n    submittedAt: new Date('2024-01-05'),\n    courseCode: 'CS301',\n    category: 'other' as const\n  }\n]\n\nfunction getStatusColor(status: string) {\n  switch (status) {\n    case 'pending':\n      return 'warning'\n    case 'in_progress':\n      return 'info'\n    case 'resolved':\n      return 'success'\n    case 'rejected':\n      return 'error'\n    default:\n      return 'default'\n  }\n}\n\nfunction formatDate(date: Date) {\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric'\n  })\n}\n\nexport default function StudentDashboard() {\n  return (\n    <div className=\"min-h-screen bg-background-secondary\">\n      <Header user={mockUser} notifications={3} />\n      \n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Welcome Section */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">Welcome back, {mockUser.name}!</h1>\n          <p className=\"text-gray-600 mt-2\">Here's an overview of your complaints and recent activity.</p>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <Card>\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm font-medium text-gray-600\">Total Complaints</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-gray-900\">{mockStats.totalComplaints}</div>\n              <p className=\"text-xs text-gray-500 mt-1\">All time</p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm font-medium text-gray-600\">Pending</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-warning\">{mockStats.pendingComplaints}</div>\n              <p className=\"text-xs text-gray-500 mt-1\">Awaiting review</p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm font-medium text-gray-600\">Resolved</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-success\">{mockStats.resolvedComplaints}</div>\n              <p className=\"text-xs text-gray-500 mt-1\">Successfully resolved</p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm font-medium text-gray-600\">Rejected</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-error\">{mockStats.rejectedComplaints}</div>\n              <p className=\"text-xs text-gray-500 mt-1\">Not approved</p>\n            </CardContent>\n          </Card>\n        </div>\n\n        <div className=\"grid lg:grid-cols-3 gap-8\">\n          {/* Recent Complaints */}\n          <div className=\"lg:col-span-2\">\n            <Card>\n              <CardHeader>\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <CardTitle>Recent Complaints</CardTitle>\n                    <CardDescription>Your latest complaint submissions</CardDescription>\n                  </div>\n                  <Link href=\"/complaints\">\n                    <Button variant=\"outline\" size=\"sm\">View All</Button>\n                  </Link>\n                </div>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {mockRecentComplaints.map((complaint) => (\n                    <div key={complaint.id} className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-3 mb-2\">\n                          <h3 className=\"font-medium text-gray-900\">{complaint.title}</h3>\n                          <Badge variant={getStatusColor(complaint.status)} size=\"sm\">\n                            {complaint.status.replace('_', ' ')}\n                          </Badge>\n                        </div>\n                        <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                          <span>ID: {complaint.complaintId}</span>\n                          <span>Course: {complaint.courseCode}</span>\n                          <span>Submitted: {formatDate(complaint.submittedAt)}</span>\n                        </div>\n                      </div>\n                      <Link href={`/complaints/${complaint.id}`}>\n                        <Button variant=\"ghost\" size=\"sm\">\n                          View Details\n                        </Button>\n                      </Link>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Quick Actions</CardTitle>\n                <CardDescription>Common tasks and shortcuts</CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-3\">\n                <Link href=\"/complaints/new\" className=\"block\">\n                  <Button className=\"w-full justify-start\" variant=\"primary\">\n                    <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n                    </svg>\n                    Submit New Complaint\n                  </Button>\n                </Link>\n                \n                <Link href=\"/complaints\" className=\"block\">\n                  <Button className=\"w-full justify-start\" variant=\"outline\">\n                    <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                    </svg>\n                    View All Complaints\n                  </Button>\n                </Link>\n\n                <Link href=\"/profile\" className=\"block\">\n                  <Button className=\"w-full justify-start\" variant=\"ghost\">\n                    <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                    </svg>\n                    Update Profile\n                  </Button>\n                </Link>\n              </CardContent>\n            </Card>\n\n            {/* Help & Support */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Need Help?</CardTitle>\n                <CardDescription>Get assistance with the complaint process</CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-3\">\n                <div className=\"text-sm text-gray-600\">\n                  <p className=\"mb-2\">Having trouble with your complaint?</p>\n                  <ul className=\"space-y-1 text-xs\">\n                    <li>• Check our FAQ section</li>\n                    <li>• Contact your department officer</li>\n                    <li>• Reach out to admin support</li>\n                  </ul>\n                </div>\n                <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                  Contact Support\n                </Button>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </main>\n\n      <Footer />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAUA,8BAA8B;AAC9B,MAAM,WAAW;IACf,MAAM;IACN,MAAM;IACN,QAAQ;AACV;AAEA,MAAM,YAAY;IAChB,iBAAiB;IACjB,mBAAmB;IACnB,oBAAoB;IACpB,oBAAoB;AACtB;AAEA,MAAM,uBAAuB;IAC3B;QACE,IAAI;QACJ,aAAa;QACb,OAAO;QACP,QAAQ;QACR,aAAa,IAAI,KAAK;QACtB,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,aAAa;QACb,OAAO;QACP,QAAQ;QACR,aAAa,IAAI,KAAK;QACtB,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,aAAa;QACb,OAAO;QACP,QAAQ;QACR,aAAa,IAAI,KAAK;QACtB,YAAY;QACZ,UAAU;IACZ;CACD;AAED,SAAS,eAAe,MAAc;IACpC,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,SAAS,WAAW,IAAU;IAC5B,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAU,eAAe;;;;;;0BAEvC,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAmC;oCAAe,SAAS,IAAI;oCAAC;;;;;;;0CAC9E,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAoC;;;;;;;;;;;kDAE3D,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;0DAAoC,UAAU,eAAe;;;;;;0DAC5E,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;0CAI9C,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAoC;;;;;;;;;;;kDAE3D,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;0DAAmC,UAAU,iBAAiB;;;;;;0DAC7E,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;0CAI9C,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAoC;;;;;;;;;;;kDAE3D,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;0DAAmC,UAAU,kBAAkB;;;;;;0DAC9E,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;0CAI9C,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAoC;;;;;;;;;;;kDAE3D,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;0DAAiC,UAAU,kBAAkB;;;;;;0DAC5E,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;;;;;;kCAKhD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC,mIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,6LAAC,mIAAA,CAAA,kBAAe;0EAAC;;;;;;;;;;;;kEAEnB,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;kEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;sEAAK;;;;;;;;;;;;;;;;;;;;;;sDAI1C,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;0DACZ,qBAAqB,GAAG,CAAC,CAAC,0BACzB,6LAAC;wDAAuB,WAAU;;0EAChC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAG,WAAU;0FAA6B,UAAU,KAAK;;;;;;0FAC1D,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAS,eAAe,UAAU,MAAM;gFAAG,MAAK;0FACpD,UAAU,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;kFAGnC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;oFAAK;oFAAK,UAAU,WAAW;;;;;;;0FAChC,6LAAC;;oFAAK;oFAAS,UAAU,UAAU;;;;;;;0FACnC,6LAAC;;oFAAK;oFAAY,WAAW,UAAU,WAAW;;;;;;;;;;;;;;;;;;;0EAGtD,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,YAAY,EAAE,UAAU,EAAE,EAAE;0EACvC,cAAA,6LAAC,qIAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAQ,MAAK;8EAAK;;;;;;;;;;;;uDAf5B,UAAU,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;0CA2BhC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;;kEACT,6LAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,mIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAEnB,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAkB,WAAU;kEACrC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;4DAAuB,SAAQ;;8EAC/C,6LAAC;oEAAI,WAAU;oEAAe,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACtE,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;gEACjE;;;;;;;;;;;;kEAKV,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAc,WAAU;kEACjC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;4DAAuB,SAAQ;;8EAC/C,6LAAC;oEAAI,WAAU;oEAAe,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACtE,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;gEACjE;;;;;;;;;;;;kEAKV,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAC9B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;4DAAuB,SAAQ;;8EAC/C,6LAAC;oEAAI,WAAU;oEAAe,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACtE,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;gEACjE;;;;;;;;;;;;;;;;;;;;;;;;kDAQd,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;;kEACT,6LAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,mIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAEnB,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAO;;;;;;0EACpB,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;kFAAG;;;;;;kFACJ,6LAAC;kFAAG;;;;;;kFACJ,6LAAC;kFAAG;;;;;;;;;;;;;;;;;;kEAGR,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,MAAK;wDAAK,WAAU;kEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjE,6LAAC,yIAAA,CAAA,SAAM;;;;;;;;;;;AAGb;KAnKwB", "debugId": null}}]}