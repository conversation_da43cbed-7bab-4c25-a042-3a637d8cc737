{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/complaint_sys/nahpi-complains/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date | string): string {\n  const d = new Date(date)\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nexport function formatDateTime(date: Date | string): string {\n  const d = new Date(date)\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  })\n}\n\nexport function getStatusColor(status: string): string {\n  switch (status.toLowerCase()) {\n    case 'pending':\n    case 'unresolved':\n      return 'bg-yellow-100 text-yellow-800 border-yellow-200'\n    case 'in progress':\n    case 'processing':\n      return 'bg-blue-100 text-blue-800 border-blue-200'\n    case 'resolved':\n      return 'bg-green-100 text-green-800 border-green-200'\n    case 'rejected':\n      return 'bg-red-100 text-red-800 border-red-200'\n    default:\n      return 'bg-gray-100 text-gray-800 border-gray-200'\n  }\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase())\n    .join('')\n    .slice(0, 2)\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function validatePhone(phone: string): boolean {\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/\n  return phoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\nexport function generateComplaintId(): string {\n  const timestamp = Date.now().toString(36)\n  const random = Math.random().toString(36).substr(2, 5)\n  return `CMP-${timestamp}-${random}`.toUpperCase()\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength) + '...'\n}\n\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAQ,OAAO,WAAW;QACxB,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC,IACL,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAEO,SAAS;IACd,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,CAAC;IACtC,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;IACpD,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,QAAQ,CAAC,WAAW;AACjD;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;AAC/D;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/complaint_sys/nahpi-complains/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'\n  size?: 'sm' | 'md' | 'lg'\n  isLoading?: boolean\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ \n    className, \n    variant = 'primary', \n    size = 'md', \n    isLoading = false,\n    leftIcon,\n    rightIcon,\n    children, \n    disabled,\n    ...props \n  }, ref) => {\n    const baseStyles = \"inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none\"\n    \n    const variants = {\n      primary: \"bg-primary text-white hover:bg-primary-dark focus:ring-primary\",\n      secondary: \"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500\",\n      outline: \"border border-primary text-primary hover:bg-primary hover:text-white focus:ring-primary\",\n      ghost: \"text-primary hover:bg-accent-blue focus:ring-primary\",\n      danger: \"bg-error text-white hover:bg-red-600 focus:ring-error\"\n    }\n    \n    const sizes = {\n      sm: \"h-8 px-3 text-sm\",\n      md: \"h-10 px-4 text-sm\",\n      lg: \"h-12 px-6 text-base\"\n    }\n\n    return (\n      <button\n        className={cn(\n          baseStyles,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {!isLoading && leftIcon && (\n          <span className=\"mr-2\">{leftIcon}</span>\n        )}\n        {children}\n        {!isLoading && rightIcon && (\n          <span className=\"ml-2\">{rightIcon}</span>\n        )}\n      </button>\n    )\n  }\n)\n\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAUA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EACC,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,KAAK,EACjB,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,aAAa;IAEnB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,2BACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP,CAAC,aAAa,0BACb,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;YAEzB;YACA,CAAC,aAAa,2BACb,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAIhC;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/complaint_sys/nahpi-complains/src/components/ui/Input.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string\n  error?: string\n  helperText?: string\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ \n    className, \n    type = 'text',\n    label,\n    error,\n    helperText,\n    leftIcon,\n    rightIcon,\n    id,\n    ...props \n  }, ref) => {\n    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`\n    \n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label \n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700 mb-1\"\n          >\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <span className=\"text-gray-400 text-sm\">{leftIcon}</span>\n            </div>\n          )}\n          <input\n            type={type}\n            id={inputId}\n            className={cn(\n              \"block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm placeholder-gray-400 shadow-sm transition-colors\",\n              \"focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary\",\n              \"disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-500\",\n              error && \"border-error focus:border-error focus:ring-error\",\n              leftIcon && \"pl-10\",\n              rightIcon && \"pr-10\",\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n          {rightIcon && (\n            <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n              <span className=\"text-gray-400 text-sm\">{rightIcon}</span>\n            </div>\n          )}\n        </div>\n        {error && (\n          <p className=\"mt-1 text-sm text-error\">{error}</p>\n        )}\n        {helperText && !error && (\n          <p className=\"mt-1 text-sm text-gray-500\">{helperText}</p>\n        )}\n      </div>\n    )\n  }\n)\n\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAUA,MAAM,sBAAQ,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC5B,CAAC,EACC,SAAS,EACT,OAAO,MAAM,EACb,KAAK,EACL,KAAK,EACL,UAAU,EACV,QAAQ,EACR,SAAS,EACT,EAAE,EACF,GAAG,OACJ,EAAE;IACD,MAAM,UAAU,MAAM,CAAC,MAAM,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAExE,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;oBACZ,0BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAyB;;;;;;;;;;;kCAG7C,6LAAC;wBACC,MAAM;wBACN,IAAI;wBACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qHACA,2EACA,0EACA,SAAS,oDACT,YAAY,SACZ,aAAa,SACb;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;oBAEV,2BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAyB;;;;;;;;;;;;;;;;;YAI9C,uBACC,6LAAC;gBAAE,WAAU;0BAA2B;;;;;;YAEzC,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/complaint_sys/nahpi-complains/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'default' | 'outlined' | 'elevated'\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant = 'default', ...props }, ref) => {\n    const variants = {\n      default: \"bg-white border border-gray-200\",\n      outlined: \"bg-white border-2 border-gray-300\",\n      elevated: \"bg-white shadow-lg border border-gray-100\"\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"rounded-lg\",\n          variants[variant],\n          className\n        )}\n        {...props}\n      />\n    )\n  }\n)\n\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n      {...props}\n    />\n  )\n)\n\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn(\"text-lg font-semibold leading-none tracking-tight text-gray-900\", className)}\n      {...props}\n    />\n  )\n)\n\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p\n      ref={ref}\n      className={cn(\"text-sm text-gray-600\", className)}\n      {...props}\n    />\n  )\n)\n\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div \n      ref={ref} \n      className={cn(\"p-6 pt-0\", className)} \n      {...props} \n    />\n  )\n)\n\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\"flex items-center p-6 pt-0\", className)}\n      {...props}\n    />\n  )\n)\n\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAMA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAO,EAAE;IAC7C,MAAM,WAAW;QACf,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cACA,QAAQ,CAAC,QAAQ,EACjB;QAED,GAAG,KAAK;;;;;;AAGf;;AAGF,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAKf,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAChC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;;;;;;;AAKf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OACtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAKf,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;;AAKf,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QACjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAKf,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 422, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/complaint_sys/nahpi-complains/src/app/login/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\n\nexport default function LoginPage() {\n  const [formData, setFormData] = useState({\n    matricule: '',  \n    password: ''\n  })\n  const [isLoading, setIsLoading] = useState(false)\n  const [errors, setErrors] = useState<Record<string, string>>({})\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target\n    setFormData(prev => ({ ...prev, [name]: value }))\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }))\n    }\n  }\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {}\n\n    if (!formData.matricule.trim()) {\n      newErrors.matricule = 'Matricule is required'\n    }\n\n    if (!formData.password.trim()) {\n      newErrors.password = 'Password is required'\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) return\n\n    setIsLoading(true)\n    \n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 2000))\n\n      // Here you would make the actual API call\n      console.log('Student login attempt:', formData)\n\n      // In a real app, you would redirect here\n      alert('Login successful! Redirecting to student dashboard')\n\n    } catch (error) {\n      console.error('Login error:', error)\n      setErrors({ general: 'Login failed. Please check your credentials and try again.' })\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-accent to-accent-blue py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md mx-auto\">\n        {/* Logo */}\n        <div className=\"text-center mb-8\">\n          <Link href=\"/\" className=\"inline-flex items-center space-x-2\">\n            <div className=\"w-10 h-10 bg-primary rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold\">NC</span>\n            </div>\n            <div>\n              <h1 className=\"text-2xl font-bold text-primary\">NAHPi Complaints</h1>\n              <p className=\"text-sm text-gray-600\">Student Portal</p>\n            </div>\n          </Link>\n        </div>\n\n        <Card>\n          <CardHeader className=\"text-center\">\n            <CardTitle className=\"text-2xl\">Welcome Back</CardTitle>\n            <CardDescription>\n              Sign in to your account to continue\n            </CardDescription>\n          </CardHeader>\n          \n          <CardContent>\n\n\n            {/* Login Form */}\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              {errors.general && (\n                <div className=\"p-3 bg-red-50 border border-red-200 rounded-lg\">\n                  <p className=\"text-sm text-red-600\">{errors.general}</p>\n                </div>\n              )}\n\n              <Input\n                label=\"Matricule Number\"\n                name=\"matricule\"\n                value={formData.matricule}\n                onChange={handleInputChange}\n                error={errors.matricule}\n                placeholder=\"Enter your matricule number\"\n                leftIcon={\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V4a2 2 0 00-2-2v2m0 0V4a2 2 0 012-2h2a2 2 0 012 2v2\" />\n                  </svg>\n                }\n              />\n\n              <Input\n                label=\"Password\"\n                name=\"password\"\n                type=\"password\"\n                value={formData.password}\n                onChange={handleInputChange}\n                error={errors.password}\n                placeholder=\"Enter your password\"\n                leftIcon={\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n                  </svg>\n                }\n              />\n\n              <div className=\"flex items-center justify-between\">\n                <Link \n                  href=\"/forgot-password\" \n                  className=\"text-sm text-primary hover:text-primary-dark transition-colors\"\n                >\n                  Forgot password?\n                </Link>\n              </div>\n\n              <Button \n                type=\"submit\" \n                className=\"w-full\" \n                size=\"lg\"\n                isLoading={isLoading}\n              >\n                {isLoading ? 'Signing in...' : 'Sign In'}\n              </Button>\n            </form>\n\n            {/* Register Link */}\n            <div className=\"mt-6 text-center\">\n              <p className=\"text-sm text-gray-600\">\n                Don&apos;t have an account?{' '}\n                <Link\n                  href=\"/register\"\n                  className=\"text-primary hover:text-primary-dark font-medium transition-colors\"\n                >\n                  Register here\n                </Link>\n              </p>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Back to Home */}\n        <div className=\"text-center mt-6\">\n          <Link \n            href=\"/\" \n            className=\"text-sm text-gray-600 hover:text-primary transition-colors\"\n          >\n            ← Back to Home\n          </Link>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,UAAU;IACZ;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;QAC/C,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAG,CAAC;QAC5C;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI;YAC9B,UAAU,SAAS,GAAG;QACxB;QAEA,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YAC7B,UAAU,QAAQ,GAAG;QACvB,OAAO,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YACvC,UAAU,QAAQ,GAAG;QACvB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,aAAa;QAEb,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,0CAA0C;YAC1C,QAAQ,GAAG,CAAC,0BAA0B;YAEtC,yCAAyC;YACzC,MAAM;QAER,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,UAAU;gBAAE,SAAS;YAA6D;QACpF,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAuB;;;;;;;;;;;0CAEzC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;8BAK3C,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;4BAAC,WAAU;;8CACpB,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAW;;;;;;8CAChC,6LAAC,mIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAKnB,6LAAC,mIAAA,CAAA,cAAW;;8CAIV,6LAAC;oCAAK,UAAU;oCAAc,WAAU;;wCACrC,OAAO,OAAO,kBACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DAAwB,OAAO,OAAO;;;;;;;;;;;sDAIvD,6LAAC,oIAAA,CAAA,QAAK;4CACJ,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,SAAS;4CACzB,UAAU;4CACV,OAAO,OAAO,SAAS;4CACvB,aAAY;4CACZ,wBACE,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAK3E,6LAAC,oIAAA,CAAA,QAAK;4CACJ,OAAM;4CACN,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,OAAO,OAAO,QAAQ;4CACtB,aAAY;4CACZ,wBACE,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAK3E,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAKH,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,WAAU;4CACV,MAAK;4CACL,WAAW;sDAEV,YAAY,kBAAkB;;;;;;;;;;;;8CAKnC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;4CAAwB;4CACP;0DAC5B,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAST,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX;GAzKwB;KAAA", "debugId": null}}]}