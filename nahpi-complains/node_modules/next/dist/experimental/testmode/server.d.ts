import type { WorkerRe<PERSON><PERSON><PERSON><PERSON> } from '../../server/lib/types';
import type { NodeRequestHandler } from '../../server/next-server';
export declare function interceptTestApis(): () => void;
export declare function wrapRequestHandlerWorker(handler: WorkerRequestHand<PERSON>): WorkerRequestHandler;
export declare function wrapRequestHandlerNode(handler: NodeRequestHandler): NodeRequestHandler;
