'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AuthService } from '@/lib/auth';
import { apiClient, API_ENDPOINTS } from '@/lib/api';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';

interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
}

interface AdminDashboardStats {
  complaints: {
    total: number;
    pending: number;
    resolved: number;
    overdue: number;
    urgent: number;
  };
  users: {
    total: number;
    active: number;
    verified: number;
    students: number;
  };
  departments: {
    total: number;
    active: number;
  };
  recent_activity: {
    new_complaints: number;
    resolved_complaints: number;
    new_users: number;
  };
  top_complaint_types: Array<{
    complaint_type: string;
    count: number;
  }>;
  recent_complaints: any[];
}

export default function SimpleAdminDashboard() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [stats, setStats] = useState<AdminDashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadDashboard = async () => {
      try {
        // Check if user is authenticated
        if (!AuthService.isAuthenticated()) {
          router.push('/admin/login');
          return;
        }

        // Get user profile
        const userProfile = await AuthService.getProfile();
        setUser(userProfile);

        // Check if user is admin
        if (userProfile.role !== 'admin') {
          router.push('/admin/login');
          return;
        }

        // Get admin dashboard stats
        const dashboardStats = await apiClient.get<AdminDashboardStats>(
          API_ENDPOINTS.COMPLAINTS.DASHBOARD_ADMIN
        );
        setStats(dashboardStats);

      } catch (error: any) {
        console.error('Failed to load admin dashboard:', error);
        setError(error.message || 'Failed to load dashboard');
        
        // If authentication error, redirect to login
        if (error.status === 401 || error.status === 403) {
          router.push('/admin/login');
        }
      } finally {
        setLoading(false);
      }
    };

    loadDashboard();
  }, [router]);

  const handleLogout = async () => {
    await AuthService.logout();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-red-600">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => router.push('/admin/login')} className="w-full">
              Go to Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-gray-600">System Administration Panel</p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">
                Welcome, {user?.first_name} {user?.last_name}
              </span>
              <Button variant="outline" onClick={handleLogout}>
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Manage Users</CardTitle>
              </CardHeader>
              <CardContent>
                <Button size="sm" className="w-full">
                  User Management
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">View All Complaints</CardTitle>
              </CardHeader>
              <CardContent>
                <Button size="sm" variant="outline" className="w-full">
                  All Complaints
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <Button size="sm" variant="outline" className="w-full">
                  View Reports
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <Button size="sm" variant="outline" className="w-full">
                  System Settings
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Statistics Overview */}
          {stats && (
            <>
              {/* Complaint Statistics */}
              <div className="mb-8">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Complaint Statistics</h2>
                <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Total</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-blue-600">
                        {stats.complaints.total}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Pending</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-yellow-600">
                        {stats.complaints.pending}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Resolved</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-green-600">
                        {stats.complaints.resolved}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Overdue</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-red-600">
                        {stats.complaints.overdue}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Urgent</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-orange-600">
                        {stats.complaints.urgent}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>

              {/* User & Department Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                <Card>
                  <CardHeader>
                    <CardTitle>User Statistics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Total Users:</span>
                        <span className="font-semibold">{stats.users.total}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Active Users:</span>
                        <span className="font-semibold">{stats.users.active}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Verified Users:</span>
                        <span className="font-semibold">{stats.users.verified}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Students:</span>
                        <span className="font-semibold">{stats.users.students}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Recent Activity (Last 7 Days)</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>New Complaints:</span>
                        <span className="font-semibold">{stats.recent_activity.new_complaints}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Resolved Complaints:</span>
                        <span className="font-semibold">{stats.recent_activity.resolved_complaints}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>New Users:</span>
                        <span className="font-semibold">{stats.recent_activity.new_users}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Recent Complaints */}
              {stats.recent_complaints.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Complaints</CardTitle>
                    <CardDescription>Latest complaint submissions</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {stats.recent_complaints.slice(0, 5).map((complaint: any) => (
                        <div key={complaint.id} className="border-l-4 border-blue-500 pl-4">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="font-semibold">{complaint.title}</h4>
                              <p className="text-sm text-gray-600">
                                {complaint.complaint_number} • {complaint.complainant_name} • {complaint.department_name}
                              </p>
                            </div>
                            <span className={`px-2 py-1 rounded text-xs font-medium ${
                              complaint.status === 'resolved' ? 'bg-green-100 text-green-800' :
                              complaint.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                              complaint.status === 'under_review' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {complaint.status.replace('_', ' ')}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </div>
      </main>
    </div>
  );
}
