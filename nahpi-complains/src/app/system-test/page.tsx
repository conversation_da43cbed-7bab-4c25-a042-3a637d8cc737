'use client';

import { useState } from 'react';
import { AuthService } from '@/lib/auth';
import { apiClient, API_ENDPOINTS } from '@/lib/api';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error' | 'skipped';
  message: string;
  data?: any;
  duration?: number;
}

export default function SystemTestPage() {
  const [tests, setTests] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);

  const updateTest = (name: string, status: TestResult['status'], message: string, data?: any, duration?: number) => {
    setTests(prev => {
      const existing = prev.find(t => t.name === name);
      if (existing) {
        existing.status = status;
        existing.message = message;
        existing.data = data;
        existing.duration = duration;
        return [...prev];
      } else {
        return [...prev, { name, status, message, data, duration }];
      }
    });
  };

  const runSystemTests = async () => {
    setIsRunning(true);
    setTests([]);
    setCurrentUser(null);

    // Test 1: Student Authentication with Matricule
    try {
      const startTime = Date.now();
      updateTest('Student Authentication (Matricule)', 'pending', 'Testing student login with matricule number...');
      
      const studentLogin = await AuthService.login({
        matricule: 'NAH/2024/001234',
        password: 'student123'
      });
      
      setCurrentUser(studentLogin.user);
      const duration = Date.now() - startTime;
      updateTest('Student Authentication (Matricule)', 'success', 
        `Student login successful - ${studentLogin.user.first_name} ${studentLogin.user.last_name}`, 
        { user: studentLogin.user, hasTokens: !!studentLogin.access }, duration);
    } catch (error: any) {
      updateTest('Student Authentication (Matricule)', 'error', error.message || 'Student login failed');
    }

    // Test 2: Student Profile Retrieval
    try {
      const startTime = Date.now();
      updateTest('Student Profile Retrieval', 'pending', 'Getting student profile...');
      
      const profile = await AuthService.getProfile();
      const duration = Date.now() - startTime;
      updateTest('Student Profile Retrieval', 'success', 
        `Profile retrieved successfully`, profile, duration);
    } catch (error: any) {
      updateTest('Student Profile Retrieval', 'error', error.message || 'Failed to get profile');
    }

    // Test 3: Student Dashboard Data
    try {
      const startTime = Date.now();
      updateTest('Student Dashboard Data', 'pending', 'Loading student dashboard...');
      
      const dashboard = await apiClient.get(API_ENDPOINTS.COMPLAINTS.DASHBOARD_STUDENT);
      const duration = Date.now() - startTime;
      updateTest('Student Dashboard Data', 'success', 
        `Dashboard loaded - ${dashboard.total_complaints} total complaints`, dashboard, duration);
    } catch (error: any) {
      updateTest('Student Dashboard Data', 'error', error.message || 'Failed to load dashboard');
    }

    // Test 4: Complaint Submission
    try {
      const startTime = Date.now();
      updateTest('Complaint Submission', 'pending', 'Submitting test complaint...');
      
      const complaintData = {
        title: `System Test Complaint - ${new Date().toISOString()}`,
        description: 'This is an automated test complaint to verify the complaint submission system.',
        complaint_type: 'facility',
        department_code: 'CSC',
        incident_location: 'Computer Lab 1',
        is_urgent: false
      };
      
      const complaint = await apiClient.post(API_ENDPOINTS.COMPLAINTS.CREATE, complaintData);
      const duration = Date.now() - startTime;
      updateTest('Complaint Submission', 'success', 
        `Complaint submitted - ${complaint.complaint_number}`, complaint, duration);
    } catch (error: any) {
      updateTest('Complaint Submission', 'error', error.message || 'Failed to submit complaint');
    }

    // Test 5: Complaints List Retrieval
    try {
      const startTime = Date.now();
      updateTest('Complaints List Retrieval', 'pending', 'Getting user complaints...');
      
      const complaints = await apiClient.get(API_ENDPOINTS.COMPLAINTS.LIST);
      const duration = Date.now() - startTime;
      updateTest('Complaints List Retrieval', 'success', 
        `Retrieved ${complaints.results?.length || complaints.length || 0} complaints`, complaints, duration);
    } catch (error: any) {
      updateTest('Complaints List Retrieval', 'error', error.message || 'Failed to get complaints');
    }

    // Test 6: Notifications
    try {
      const startTime = Date.now();
      updateTest('Notifications System', 'pending', 'Testing notifications...');
      
      const notifications = await apiClient.get(API_ENDPOINTS.NOTIFICATIONS.LIST);
      const duration = Date.now() - startTime;
      updateTest('Notifications System', 'success', 
        `Retrieved ${notifications.results?.length || notifications.length || 0} notifications`, notifications, duration);
    } catch (error: any) {
      updateTest('Notifications System', 'error', error.message || 'Failed to get notifications');
    }

    // Test 7: Departments List
    try {
      const startTime = Date.now();
      updateTest('Departments List', 'pending', 'Getting departments...');
      
      const departments = await AuthService.getDepartments();
      const duration = Date.now() - startTime;
      updateTest('Departments List', 'success', 
        `Retrieved ${departments.length} departments`, departments, duration);
    } catch (error: any) {
      updateTest('Departments List', 'error', error.message || 'Failed to get departments');
    }

    // Test 8: Admin Authentication
    try {
      const startTime = Date.now();
      updateTest('Admin Authentication', 'pending', 'Testing admin login...');
      
      // Logout current user first
      await AuthService.logout();
      
      const adminLogin = await AuthService.login({
        email: '<EMAIL>',
        password: 'admin123'
      }, true);
      
      const duration = Date.now() - startTime;
      updateTest('Admin Authentication', 'success', 
        `Admin login successful - ${adminLogin.user.first_name} ${adminLogin.user.last_name}`, 
        { user: adminLogin.user, hasTokens: !!adminLogin.access }, duration);
    } catch (error: any) {
      updateTest('Admin Authentication', 'error', error.message || 'Admin login failed');
    }

    // Test 9: Admin Dashboard
    try {
      const startTime = Date.now();
      updateTest('Admin Dashboard', 'pending', 'Loading admin dashboard...');
      
      const adminDashboard = await apiClient.get(API_ENDPOINTS.COMPLAINTS.DASHBOARD_ADMIN);
      const duration = Date.now() - startTime;
      updateTest('Admin Dashboard', 'success', 
        `Admin dashboard loaded - ${adminDashboard.complaints?.total || 0} total complaints`, 
        adminDashboard, duration);
    } catch (error: any) {
      updateTest('Admin Dashboard', 'error', error.message || 'Failed to load admin dashboard');
    }

    // Test 10: All Complaints (Admin View)
    try {
      const startTime = Date.now();
      updateTest('All Complaints (Admin)', 'pending', 'Getting all complaints as admin...');
      
      const allComplaints = await apiClient.get(API_ENDPOINTS.COMPLAINTS.LIST);
      const duration = Date.now() - startTime;
      updateTest('All Complaints (Admin)', 'success', 
        `Retrieved ${allComplaints.results?.length || allComplaints.length || 0} complaints`, 
        allComplaints, duration);
    } catch (error: any) {
      updateTest('All Complaints (Admin)', 'error', error.message || 'Failed to get all complaints');
    }

    setIsRunning(false);
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'pending': return 'text-yellow-600';
      case 'skipped': return 'text-gray-500';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'pending': return '⏳';
      case 'skipped': return '⏭️';
      default: return '⚪';
    }
  };

  const successCount = tests.filter(t => t.status === 'success').length;
  const errorCount = tests.filter(t => t.status === 'error').length;
  const totalTests = tests.length;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            NAHPi Complains - Complete System Test
          </h1>
          <p className="text-gray-600 mb-6">
            Comprehensive end-to-end testing of all system functionalities including authentication, 
            complaint management, dashboards, and API integrations.
          </p>
          
          <div className="flex items-center space-x-4 mb-6">
            <Button 
              onClick={runSystemTests} 
              disabled={isRunning}
              className="px-6 py-2"
            >
              {isRunning ? 'Running Tests...' : 'Run Complete System Test'}
            </Button>
            
            {totalTests > 0 && (
              <div className="flex items-center space-x-4 text-sm">
                <span className="text-green-600">✅ {successCount} passed</span>
                <span className="text-red-600">❌ {errorCount} failed</span>
                <span className="text-gray-600">Total: {totalTests}</span>
              </div>
            )}
          </div>
        </div>

        {/* Current User Info */}
        {currentUser && (
          <Card className="mb-6 border-blue-200">
            <CardHeader>
              <CardTitle className="text-blue-800">Current Test User</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="font-medium">Name:</span> {currentUser.first_name} {currentUser.last_name}
                </div>
                <div>
                  <span className="font-medium">Role:</span> {currentUser.role}
                </div>
                <div>
                  <span className="font-medium">Email:</span> {currentUser.email}
                </div>
                <div>
                  <span className="font-medium">Student ID:</span> {currentUser.student_id || 'N/A'}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Test Results */}
        <div className="space-y-4">
          {tests.map((test, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span>{getStatusIcon(test.status)}</span>
                    <span>{test.name}</span>
                    <span className={`text-sm ${getStatusColor(test.status)}`}>
                      ({test.status})
                    </span>
                  </div>
                  {test.duration && (
                    <span className="text-xs text-gray-500">{test.duration}ms</span>
                  )}
                </CardTitle>
                <CardDescription>{test.message}</CardDescription>
              </CardHeader>
              {test.data && (
                <CardContent>
                  <details className="mt-2">
                    <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
                      View Response Data
                    </summary>
                    <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto max-h-40">
                      {JSON.stringify(test.data, null, 2)}
                    </pre>
                  </details>
                </CardContent>
              )}
            </Card>
          ))}
        </div>

        {tests.length === 0 && !isRunning && (
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-gray-500">Click "Run Complete System Test" to start comprehensive testing.</p>
            </CardContent>
          </Card>
        )}

        {/* Test Information */}
        <div className="mt-8 p-6 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-4">System Test Coverage:</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
            <div>
              <h4 className="font-medium mb-2">Authentication Tests:</h4>
              <ul className="space-y-1 ml-4">
                <li>• Student login with matricule number</li>
                <li>• Admin login with email</li>
                <li>• Profile retrieval and validation</li>
                <li>• JWT token management</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Functionality Tests:</h4>
              <ul className="space-y-1 ml-4">
                <li>• Complaint submission and retrieval</li>
                <li>• Dashboard data loading</li>
                <li>• Notifications system</li>
                <li>• Department management</li>
              </ul>
            </div>
          </div>
          <div className="mt-4 text-xs text-blue-700">
            <p><strong>Backend:</strong> http://127.0.0.1:8000/api</p>
            <p><strong>Frontend:</strong> http://localhost:3000</p>
            <p><strong>Test Accounts:</strong> NAH/2024/001234/student123, <EMAIL>/admin123</p>
          </div>
        </div>
      </div>
    </div>
  );
}
