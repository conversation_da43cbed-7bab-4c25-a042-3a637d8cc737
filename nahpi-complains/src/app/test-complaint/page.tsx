'use client';

import { useState } from 'react';
import { AuthService } from '@/lib/auth';
import { apiClient, API_ENDPOINTS } from '@/lib/api';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';

export default function TestComplaintPage() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [complaintData, setComplaintData] = useState({
    title: 'Test Complaint - Broken Projector',
    description: 'The projector in Lecture Hall 1 is not working properly. Students cannot see presentations clearly.',
    complaint_type: 'facility',
    department_code: 'CSC',
    incident_location: 'Lecture Hall 1',
    is_urgent: true
  });
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleLogin = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await AuthService.login({
        matricule: 'NAH/2024/001234',
        password: 'student123'
      });
      
      setUser(response.user);
      setIsLoggedIn(true);
      setResult({ type: 'login', data: response });
    } catch (error: any) {
      setError(error.message || 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitComplaint = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiClient.post(API_ENDPOINTS.COMPLAINTS.CREATE, complaintData);
      setResult({ type: 'complaint', data: response });
    } catch (error: any) {
      setError(error.message || 'Failed to submit complaint');
    } finally {
      setLoading(false);
    }
  };

  const handleGetComplaints = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiClient.get(API_ENDPOINTS.COMPLAINTS.LIST);
      setResult({ type: 'complaints_list', data: response });
    } catch (error: any) {
      setError(error.message || 'Failed to get complaints');
    } finally {
      setLoading(false);
    }
  };

  const handleGetDashboard = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiClient.get(API_ENDPOINTS.COMPLAINTS.DASHBOARD_STUDENT);
      setResult({ type: 'dashboard', data: response });
    } catch (error: any) {
      setError(error.message || 'Failed to get dashboard');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    await AuthService.logout();
    setIsLoggedIn(false);
    setUser(null);
    setResult(null);
    setError(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Complaint System Integration Test
          </h1>
          <p className="text-gray-600 mb-6">
            Test the complete complaint submission and management workflow.
          </p>
        </div>

        {/* Authentication Status */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Authentication Status</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoggedIn ? (
              <div className="space-y-2">
                <p className="text-green-600">✅ Logged in as: {user?.first_name} {user?.last_name}</p>
                <p className="text-sm text-gray-600">Role: {user?.role} | Student ID: {user?.student_id}</p>
                <Button onClick={handleLogout} variant="outline" size="sm">
                  Logout
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                <p className="text-red-600">❌ Not logged in</p>
                <Button onClick={handleLogin} disabled={loading}>
                  Login as Test Student
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Test Actions */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Test Actions</CardTitle>
            <CardDescription>Test various complaint system functionalities</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Button 
                onClick={handleSubmitComplaint} 
                disabled={!isLoggedIn || loading}
                className="h-auto p-4 flex flex-col items-start"
              >
                <span className="font-semibold">Submit Complaint</span>
                <span className="text-xs opacity-75">Create a test complaint</span>
              </Button>
              
              <Button 
                onClick={handleGetComplaints} 
                disabled={!isLoggedIn || loading}
                variant="outline"
                className="h-auto p-4 flex flex-col items-start"
              >
                <span className="font-semibold">Get My Complaints</span>
                <span className="text-xs opacity-75">List user complaints</span>
              </Button>
              
              <Button 
                onClick={handleGetDashboard} 
                disabled={!isLoggedIn || loading}
                variant="outline"
                className="h-auto p-4 flex flex-col items-start"
              >
                <span className="font-semibold">Get Dashboard</span>
                <span className="text-xs opacity-75">Student dashboard data</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Complaint Data Preview */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Test Complaint Data</CardTitle>
            <CardDescription>Data that will be submitted</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Input
                label="Title"
                value={complaintData.title}
                onChange={(e) => setComplaintData(prev => ({ ...prev, title: e.target.value }))}
              />
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  className="w-full p-2 border border-gray-300 rounded-md"
                  rows={3}
                  value={complaintData.description}
                  onChange={(e) => setComplaintData(prev => ({ ...prev, description: e.target.value }))}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <Input
                  label="Type"
                  value={complaintData.complaint_type}
                  onChange={(e) => setComplaintData(prev => ({ ...prev, complaint_type: e.target.value }))}
                />
                <Input
                  label="Department"
                  value={complaintData.department_code}
                  onChange={(e) => setComplaintData(prev => ({ ...prev, department_code: e.target.value }))}
                />
              </div>
              <Input
                label="Location"
                value={complaintData.incident_location}
                onChange={(e) => setComplaintData(prev => ({ ...prev, incident_location: e.target.value }))}
              />
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={complaintData.is_urgent}
                  onChange={(e) => setComplaintData(prev => ({ ...prev, is_urgent: e.target.checked }))}
                  className="mr-2"
                />
                Mark as urgent
              </label>
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Card className="mb-6 border-red-200">
            <CardContent className="pt-6">
              <div className="flex items-center text-red-600">
                <span className="mr-2">❌</span>
                <span>{error}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Results Display */}
        {result && (
          <Card>
            <CardHeader>
              <CardTitle>
                {result.type === 'login' && 'Login Result'}
                {result.type === 'complaint' && 'Complaint Submission Result'}
                {result.type === 'complaints_list' && 'Complaints List Result'}
                {result.type === 'dashboard' && 'Dashboard Data Result'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto max-h-96">
                {JSON.stringify(result.data, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}

        {/* Loading Indicator */}
        {loading && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg">
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
                <span>Processing...</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
