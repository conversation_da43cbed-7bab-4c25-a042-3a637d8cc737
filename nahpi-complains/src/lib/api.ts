/**
 * API Configuration and Client for NAHPi Complains Frontend
 */

// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000/api',
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
};

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/auth/login/',
    ADMIN_LOGIN: '/auth/login/admin/',
    DEPARTMENT_LOGIN: '/auth/login/department/',
    REGISTER: '/auth/register/',
    REFRESH: '/auth/token/refresh/',
    PROFILE: '/auth/profile/',
    CHANGE_PASSWORD: '/auth/change-password/',
    LOGOUT: '/auth/logout/',
    VERIFY_EMAIL: '/auth/verify-email/',
    VERIFY_PHONE: '/auth/verify-phone/',
    DEPARTMENTS: '/auth/departments/',
    CHECK_EMAIL: '/auth/check-email/',
    CHECK_STUDENT_ID: '/auth/check-student-id/',
  },
  
  // Complaints
  COMPLAINTS: {
    LIST: '/complaints/',
    CREATE: '/complaints/',
    DETAIL: (id: string) => `/complaints/${id}/`,
    UPDATE_STATUS: (id: string) => `/complaints/${id}/status/`,
    ASSIGN: (id: string) => `/complaints/${id}/assign/`,
    COMMENTS: (id: string) => `/complaints/${id}/comments/`,
    FEEDBACK: (id: string) => `/complaints/${id}/feedback/`,
    ATTACHMENTS: (id: string) => `/complaints/${id}/attachments/`,
    DOWNLOAD: (complaintId: string, attachmentId: string) => 
      `/complaints/${complaintId}/attachments/${attachmentId}/download/`,
    PREVIEW: (complaintId: string, attachmentId: string) => 
      `/complaints/${complaintId}/attachments/${attachmentId}/preview/`,
    ANALYTICS: '/complaints/analytics/',
    REPORTS: '/complaints/reports/',
    DASHBOARD_STUDENT: '/complaints/dashboard/student/',
    DASHBOARD_ADMIN: '/complaints/dashboard/admin/',
  },
  
  // Notifications
  NOTIFICATIONS: {
    LIST: '/notifications/',
    DETAIL: (id: string) => `/notifications/${id}/`,
    MARK_READ: (id: string) => `/notifications/${id}/read/`,
    MARK_ALL_READ: '/notifications/mark-all-read/',
    SUMMARY: '/notifications/summary/',
    PREFERENCES: '/notifications/preferences/',
    ADMIN_LIST: '/notifications/admin/',
    BULK_SEND: '/notifications/admin/bulk/',
    ANALYTICS: '/notifications/admin/analytics/',
  },
  
  // Admin User Management
  ADMIN: {
    USERS: '/auth/admin/users/',
    USER_DETAIL: (id: string) => `/auth/admin/users/${id}/`,
    USER_CREATE: '/auth/admin/users/create/',
    RESET_PASSWORD: (id: string) => `/auth/admin/users/${id}/reset-password/`,
    TOGGLE_STATUS: (id: string) => `/auth/admin/users/${id}/toggle-status/`,
    VERIFY_USER: (id: string) => `/auth/admin/users/${id}/verify/`,
    DEPARTMENTS: '/auth/admin/departments/',
    DEPARTMENT_DETAIL: (id: string) => `/auth/admin/departments/${id}/`,
    DASHBOARD_STATS: '/auth/admin/dashboard/stats/',
    BULK_CREATE_USERS: '/auth/admin/bulk/create-users/',
    EXPORT_USERS: '/auth/admin/export/users/',
    BULK_NOTIFICATION: '/auth/admin/bulk/notification/',
    USER_REPORTS: '/auth/admin/reports/users/',
  },
  
  // File Management
  FILES: {
    UPLOAD_INFO: '/complaints/files/upload-info/',
    VALIDATE: '/complaints/files/validate/',
    STATS: '/complaints/files/stats/',
  },
  
  // Documentation
  DOCS: {
    API_OVERVIEW: '/docs/',
    AUTH_DOCS: '/docs/auth/',
    COMPLAINTS_DOCS: '/docs/complaints/',
  }
};

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// Error Types
export interface ApiError {
  message: string;
  status: number;
  details?: Record<string, string[]>;
}

// Response Types
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: string;
  details?: Record<string, string[]>;
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// Token Management
export class TokenManager {
  private static ACCESS_TOKEN_KEY = 'nahpi_access_token';
  private static REFRESH_TOKEN_KEY = 'nahpi_refresh_token';
  
  static getAccessToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(this.ACCESS_TOKEN_KEY);
  }
  
  static getRefreshToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }
  
  static setTokens(accessToken: string, refreshToken: string): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
  }
  
  static clearTokens(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(this.ACCESS_TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
  }
  
  static isAuthenticated(): boolean {
    return !!this.getAccessToken();
  }
}

// API Client Class
export class ApiClient {
  private baseURL: string;
  private timeout: number;
  
  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
    this.timeout = API_CONFIG.TIMEOUT;
  }
  
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const accessToken = TokenManager.getAccessToken();
    
    const config: RequestInit = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
        ...options.headers,
      },
    };
    
    try {
      const response = await fetch(url, config);
      
      // Handle token refresh for 401 errors
      if (response.status === HTTP_STATUS.UNAUTHORIZED && accessToken) {
        const refreshed = await this.refreshToken();
        if (refreshed) {
          // Retry the original request with new token
          const newToken = TokenManager.getAccessToken();
          const retryConfig = {
            ...config,
            headers: {
              ...config.headers,
              Authorization: `Bearer ${newToken}`,
            },
          };
          const retryResponse = await fetch(url, retryConfig);
          return this.handleResponse<T>(retryResponse);
        } else {
          // Refresh failed, redirect to login
          TokenManager.clearTokens();
          if (typeof window !== 'undefined') {
            window.location.href = '/login';
          }
          throw new Error('Authentication failed');
        }
      }
      
      return this.handleResponse<T>(response);
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }
  
  private async handleResponse<T>(response: Response): Promise<T> {
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      const data = await response.json();
      
      if (!response.ok) {
        const error: ApiError = {
          message: data.error || data.message || 'An error occurred',
          status: response.status,
          details: data.details,
        };
        throw error;
      }
      
      return data;
    } else {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return response as unknown as T;
    }
  }
  
  private async refreshToken(): Promise<boolean> {
    const refreshToken = TokenManager.getRefreshToken();
    if (!refreshToken) return false;
    
    try {
      const response = await fetch(`${this.baseURL}${API_ENDPOINTS.AUTH.REFRESH}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refresh: refreshToken }),
      });
      
      if (response.ok) {
        const data = await response.json();
        TokenManager.setTokens(data.access, refreshToken);
        return true;
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
    }
    
    return false;
  }
  
  // HTTP Methods
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const url = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint;
    return this.request<T>(url, { method: 'GET' });
  }
  
  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }
  
  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }
  
  async patch<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }
  
  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
  
  // File upload method
  async uploadFile<T>(endpoint: string, formData: FormData): Promise<T> {
    const accessToken = TokenManager.getAccessToken();
    
    return this.request<T>(endpoint, {
      method: 'POST',
      headers: {
        ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
        // Don't set Content-Type for FormData, let browser set it
      },
      body: formData,
    });
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Types for API responses
export interface User {
  id: string
  email: string
  username: string
  first_name: string
  last_name: string
  full_name: string
  phone_number: string
  role: 'student' | 'admin' | 'department_officer'
  is_verified: boolean
  verification_status: string
  student_id?: string
  department?: string
  department_name?: string
  level?: string
  email_verified: boolean
  phone_verified: boolean
  created_at: string
  last_login_at?: string
}

export interface AuthResponse {
  access: string
  refresh: string
  user: User
}

export interface Department {
  id: string
  name: string
  code: string
  description: string
  email?: string
  phone_number?: string
  is_active: boolean
}

// Authentication API
export const authAPI = {
  // Student registration
  async registerStudent(data: {
    name: string
    email: string
    matricule: string
    department: string
    yearOfStudy: string
    phoneNumber: string
    academicYear: string
    password: string
    confirmPassword: string
    verificationMethod: 'email' | 'phone'
  }): Promise<{ message: string; user_id: string; email: string; phone_number: string }> {
    return apiClient.post(API_ENDPOINTS.AUTH.REGISTER, data);
  },

  // Student login
  async loginStudent(matricule: string, password: string): Promise<AuthResponse> {
    const data = await apiClient.post<AuthResponse>(API_ENDPOINTS.AUTH.LOGIN, { matricule, password });

    // Store tokens
    TokenManager.setTokens(data.access, data.refresh);
    localStorage.setItem('user', JSON.stringify(data.user));

    return data;
  },

  // Admin login
  async loginAdmin(email: string, password: string): Promise<AuthResponse> {
    const data = await apiClient.post<AuthResponse>(API_ENDPOINTS.AUTH.ADMIN_LOGIN, { email, password });

    // Store tokens
    TokenManager.setTokens(data.access, data.refresh);
    localStorage.setItem('user', JSON.stringify(data.user));

    return data;
  },

  // Department officer login
  async loginDepartmentOfficer(email: string, password: string): Promise<AuthResponse> {
    const data = await apiClient.post<AuthResponse>(API_ENDPOINTS.AUTH.DEPARTMENT_LOGIN, { email, password });

    // Store tokens
    TokenManager.setTokens(data.access, data.refresh);
    localStorage.setItem('user', JSON.stringify(data.user));

    return data;
  },

  // Logout
  async logout(): Promise<void> {
    try {
      await apiClient.post(API_ENDPOINTS.AUTH.LOGOUT, {});
    } finally {
      // Clear local storage regardless of API response
      TokenManager.clearTokens();
      localStorage.removeItem('user');
    }
  },

  // Get current user
  getCurrentUser(): User | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  },

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!TokenManager.getAccessToken();
  }
};

// Departments API
export const departmentsAPI = {
  // Get all departments
  async getAll(): Promise<{ results: Department[] }> {
    return apiClient.get(API_ENDPOINTS.AUTH.DEPARTMENTS);
  }
};
